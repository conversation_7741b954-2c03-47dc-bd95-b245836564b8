import 'package:flutter/material.dart';
import 'dart:typed_data';
import 'app_category.dart';

/// Modelo que representa una aplicación en el launcher
class AppItem {
  final String id; // ID único (generalmente packageName)
  final String name;
  final String packageName;
  final AppCategory category; // Categoría de la aplicación
  final IconData icon;
  final Color color;
  final String? description;
  final bool isFavorite;
  final bool isSystemApp;
  final bool isHidden;
  final DateTime? lastUsed;
  final Uint8List? appIcon; // Icono real de la aplicación

  const AppItem({
    required this.id,
    required this.name,
    required this.packageName,
    required this.category,
    required this.icon,
    required this.color,
    this.description,
    this.isFavorite = false,
    this.isSystemApp = false,
    this.isHidden = false,
    this.lastUsed,
    this.appIcon,
  });

  /// Crea una copia del AppItem con los campos especificados modificados
  AppItem copyWith({
    String? id,
    String? name,
    String? packageName,
    AppCategory? category,
    IconData? icon,
    Color? color,
    String? description,
    bool? isFavorite,
    bool? isSystemApp,
    bool? isHidden,
    DateTime? lastUsed,
    Uint8List? appIcon,
  }) {
    return AppItem(
      id: id ?? this.id,
      name: name ?? this.name,
      packageName: packageName ?? this.packageName,
      category: category ?? this.category,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      description: description ?? this.description,
      isFavorite: isFavorite ?? this.isFavorite,
      isSystemApp: isSystemApp ?? this.isSystemApp,
      isHidden: isHidden ?? this.isHidden,
      lastUsed: lastUsed ?? this.lastUsed,
      appIcon: appIcon ?? this.appIcon,
    );
  }

  /// Convierte el AppItem a un Map para almacenamiento
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'packageName': packageName,
      'categoryId': category.id,
      'icon': icon.codePoint,
      'color': color.toARGB32(),
      'description': description,
      'isFavorite': isFavorite,
      'isSystemApp': isSystemApp,
      'isHidden': isHidden,
      'lastUsed': lastUsed?.millisecondsSinceEpoch,
    };
  }

  /// Crea un AppItem desde un Map
  factory AppItem.fromMap(Map<String, dynamic> map, AppCategory defaultCategory) {
    return AppItem(
      id: map['id'] ?? map['packageName'] ?? '',
      name: map['name'] ?? '',
      packageName: map['packageName'] ?? '',
      category: defaultCategory, // Se debe pasar desde fuera
      icon: IconData(map['icon'] ?? Icons.apps.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(map['color'] ?? Colors.blue.toARGB32()),
      description: map['description'],
      isFavorite: map['isFavorite'] ?? false,
      isSystemApp: map['isSystemApp'] ?? false,
      isHidden: map['isHidden'] ?? false,
      lastUsed: map['lastUsed'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastUsed'])
          : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppItem && other.packageName == packageName;
  }

  @override
  int get hashCode => packageName.hashCode;

  @override
  String toString() {
    return 'AppItem(name: $name, packageName: $packageName, isFavorite: $isFavorite)';
  }
}
