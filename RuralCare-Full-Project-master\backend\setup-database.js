#!/usr/bin/env node

/**
 * 🔧 Senior Launcher Backend - Script de Configuración MySQL
 * Optimizado para BanaHosting (sin Docker, sin Redis)
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Configuración de la base de datos
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'senior_launcher',
  charset: 'utf8mb4'
};

async function setupDatabase() {
  let connection;
  
  try {
    console.log('🔌 Conectando a MySQL...');
    console.log(`   Host: ${DB_CONFIG.host}:${DB_CONFIG.port}`);
    console.log(`   Database: ${DB_CONFIG.database}`);
    console.log(`   User: ${DB_CONFIG.user}`);
    
    // Conectar a MySQL
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Conectado a MySQL correctamente');

    // Leer y ejecutar script SQL
    console.log('📋 Ejecutando script de base de datos...');
    const sqlScript = await fs.readFile(path.join(__dirname, 'database.sql'), 'utf8');
    
    // Dividir el script en statements individuales
    const statements = sqlScript
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'));

    let successCount = 0;
    let warningCount = 0;

    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          successCount++;
        } catch (error) {
          // Ignorar errores de "ya existe" pero mostrar otros
          if (error.message.includes('already exists') || 
              error.message.includes('Duplicate entry') ||
              error.message.includes('Table') ||
              error.message.includes('exists')) {
            warningCount++;
            console.log(`⚠️  ${error.message}`);
          } else {
            console.error(`❌ Error ejecutando statement: ${error.message}`);
            console.error(`   Statement: ${statement.substring(0, 100)}...`);
          }
        }
      }
    }

    console.log(`✅ Script ejecutado: ${successCount} statements exitosos, ${warningCount} warnings`);

    // Verificar tablas creadas
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📊 Tablas en la base de datos: ${tables.length}`);
    
    const tableNames = tables.map(table => Object.values(table)[0]);
    tableNames.forEach(tableName => {
      console.log(`  - ${tableName}`);
    });

    // Verificar usuario admin
    const [adminUsers] = await connection.execute(
      'SELECT username, role FROM admin_users WHERE role = "admin"'
    );
    
    console.log(`👥 Usuarios administradores: ${adminUsers.length}`);
    adminUsers.forEach(user => {
      console.log(`  - ${user.username} (${user.role})`);
    });

    // Verificar procedimientos
    const [procedures] = await connection.execute(
      'SHOW PROCEDURE STATUS WHERE Db = ?',
      [DB_CONFIG.database]
    );
    
    console.log(`⚙️  Procedimientos almacenados: ${procedures.length}`);
    procedures.forEach(proc => {
      console.log(`  - ${proc.Name}`);
    });

    console.log('\n🎉 ¡Base de datos configurada exitosamente!');
    console.log('\n📋 Información de acceso:');
    console.log(`  🌐 API URL: http://localhost:${process.env.PORT || 3000}/api`);
    console.log(`  🏥 Health Check: http://localhost:${process.env.PORT || 3000}/health`);
    console.log('\n🔐 Credenciales por defecto:');
    console.log('  👤 Usuario: admin');
    console.log('  🔑 Contraseña: admin123');
    console.log('\n⚠️  IMPORTANTE: Cambia la contraseña en producción!');
    console.log('\n🚀 Para iniciar el servidor:');
    console.log('  npm run dev');

  } catch (error) {
    console.error('❌ Error configurando la base de datos:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Sugerencias:');
      console.error('  1. Asegúrate de que MySQL esté ejecutándose');
      console.error('  2. Verifica la configuración en .env');
      console.error('  3. Verifica las credenciales de MySQL');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Error de acceso:');
      console.error('  1. Verifica el usuario y contraseña en .env');
      console.error('  2. Asegúrate de que el usuario tenga permisos');
      console.error('  3. Verifica que la base de datos exista');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('\n💡 Base de datos no encontrada:');
      console.error('  1. Crea la base de datos manualmente:');
      console.error(`     CREATE DATABASE ${DB_CONFIG.database};`);
      console.error('  2. O pide a tu proveedor que la cree');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Función para probar conexión
async function testConnection() {
  try {
    console.log('🔍 Probando conexión a MySQL...');
    
    const connection = await mysql.createConnection(DB_CONFIG);
    
    // Probar query básica
    const [result] = await connection.execute('SELECT 1 as test');
    console.log('✅ Conexión exitosa');
    
    // Verificar base de datos
    const [dbResult] = await connection.execute('SELECT DATABASE() as current_db');
    console.log(`📊 Base de datos actual: ${dbResult[0].current_db}`);
    
    // Contar dispositivos si la tabla existe
    try {
      const [deviceCount] = await connection.execute('SELECT COUNT(*) as count FROM devices');
      console.log(`📱 Dispositivos registrados: ${deviceCount[0].count}`);
    } catch (error) {
      console.log('📱 Tabla devices no encontrada (ejecuta npm run setup)');
    }
    
    await connection.end();
    return true;
  } catch (error) {
    console.error('❌ Error de conexión:', error.message);
    return false;
  }
}

// Función para mostrar información
function showInfo() {
  console.log('📋 Senior Launcher Backend - Configuración MySQL');
  console.log('');
  console.log('Comandos disponibles:');
  console.log('  npm run setup     - Configurar base de datos');
  console.log('  node setup-database.js test - Probar conexión');
  console.log('  node setup-database.js info - Mostrar esta información');
  console.log('');
  console.log('Configuración actual (.env):');
  console.log(`  DB_HOST: ${process.env.DB_HOST || 'localhost'}`);
  console.log(`  DB_PORT: ${process.env.DB_PORT || 3306}`);
  console.log(`  DB_NAME: ${process.env.DB_NAME || 'senior_launcher'}`);
  console.log(`  DB_USER: ${process.env.DB_USER || 'root'}`);
  console.log(`  DB_PASSWORD: ${process.env.DB_PASSWORD ? '***' : '(no configurado)'}`);
}

// Ejecutar según argumentos
const command = process.argv[2];

switch (command) {
  case 'test':
    testConnection();
    break;
  case 'info':
    showInfo();
    break;
  case 'setup':
  default:
    setupDatabase();
    break;
}
