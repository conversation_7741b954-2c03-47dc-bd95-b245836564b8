const mysql = require('mysql2/promise');

async function checkCommands() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'senior_launcher'
    });
    
    console.log('🔍 Verificando comandos pendientes...');
    
    const [commands] = await connection.execute(`
      SELECT id, device_id, command_type, status, created_at, payload 
      FROM remote_commands 
      WHERE status = 'pending' 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`📨 Comandos pendientes: ${commands.length}`);
    
    if (commands.length > 0) {
      commands.forEach((cmd, index) => {
        console.log(`\n${index + 1}. Comando: ${cmd.command_type}`);
        console.log(`   ID: ${cmd.id}`);
        console.log(`   Device: ${cmd.device_id}`);
        console.log(`   Estado: ${cmd.status}`);
        console.log(`   Creado: ${cmd.created_at}`);
        console.log(`   Payload: ${cmd.payload}`);
      });
    } else {
      console.log('❌ No hay comandos pendientes');
    }
    
    // Verificar dispositivos
    const [devices] = await connection.execute(`
      SELECT id, device_id, device_name, status, last_seen 
      FROM devices 
      ORDER BY last_seen DESC 
      LIMIT 5
    `);
    
    console.log(`\n📱 Dispositivos registrados: ${devices.length}`);
    devices.forEach((device, index) => {
      console.log(`\n${index + 1}. ${device.device_name || 'Sin nombre'}`);
      console.log(`   ID: ${device.device_id}`);
      console.log(`   Estado: ${device.status}`);
      console.log(`   Última conexión: ${device.last_seen}`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkCommands();
