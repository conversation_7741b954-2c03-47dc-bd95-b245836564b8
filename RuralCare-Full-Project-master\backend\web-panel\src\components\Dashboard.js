import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Tablet,
  ExitToApp,
  Person,
  Refresh,
  Dashboard as DashboardIcon,
  Devices,
  BarChart
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import DeviceList from './DeviceList';
import StatsCards from './StatsCards';

const Dashboard = () => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [currentView, setCurrentView] = useState('dashboard');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { user, logout } = useAuth();

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    handleClose();
    await logout();
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const renderContent = () => {
    switch (currentView) {
      case 'devices':
        return <DeviceList />;
      case 'stats':
        return <StatsCards />;
      case 'dashboard':
      default:
        return (
          <Box>
            <StatsCards />
            <Box sx={{ mt: 3 }}>
              <DeviceList />
            </Box>
          </Box>
        );
    }
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppBar */}
      <AppBar position="static" sx={{ background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)' }}>
        <Toolbar>
          <Tablet sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            RuralCare - Panel de Administración
          </Typography>

          {/* Botones de navegación */}
          <Button
            color="inherit"
            startIcon={<DashboardIcon />}
            onClick={() => setCurrentView('dashboard')}
            sx={{ mr: 1 }}
          >
            Dashboard
          </Button>
          <Button
            color="inherit"
            startIcon={<Devices />}
            onClick={() => setCurrentView('devices')}
            sx={{ mr: 1 }}
          >
            Dispositivos
          </Button>
          <Button
            color="inherit"
            startIcon={<BarChart />}
            onClick={() => setCurrentView('stats')}
            sx={{ mr: 2 }}
          >
            Estadísticas
          </Button>

          {/* Botón de refresh */}
          <IconButton
            color="inherit"
            onClick={handleRefresh}
            sx={{ mr: 1 }}
          >
            <Refresh />
          </IconButton>

          {/* Menú de usuario */}
          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'rgba(255,255,255,0.2)' }}>
              <Person />
            </Avatar>
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem disabled>
              <Typography variant="body2">
                {user?.username} ({user?.role})
              </Typography>
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ExitToApp sx={{ mr: 1 }} />
              Cerrar Sesión
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Contenido principal */}
      <Container maxWidth="xl" sx={{ mt: 3, mb: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress size={60} />
          </Box>
        ) : (
          renderContent()
        )}
      </Container>

      {/* Footer */}
      <Box
        component="footer"
        sx={{
          py: 2,
          px: 2,
          mt: 'auto',
          backgroundColor: '#f5f5f5',
          borderTop: '1px solid #e0e0e0',
        }}
      >
        <Container maxWidth="xl">
          <Typography variant="body2" color="text.secondary" align="center">
            Senior Launcher Backend v1.0.0 - Optimizado para BanaHosting
          </Typography>
        </Container>
      </Box>
    </Box>
  );
};

export default Dashboard;
