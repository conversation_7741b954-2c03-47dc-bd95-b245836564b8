const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const Database = require('./Database');

class AuthService {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
  }

  // 🔐 Hash de contraseña
  async hashPassword(password) {
    const saltRounds = 10;
    return await bcrypt.hash(password, saltRounds);
  }

  // 🔍 Verificar contraseña
  async verifyPassword(password, hash) {
    return await bcrypt.compare(password, hash);
  }

  // 🎫 Generar JWT token
  generateToken(payload) {
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn
    });
  }

  // 🔍 Verificar JWT token
  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      throw new Error('Token inválido');
    }
  }

  // 🔑 Hash para sesiones
  hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  // 👤 Autenticar usuario
  async authenticateUser(username, password) {
    try {
      // Buscar usuario
      const user = await Database.findOne(
        'admin_users',
        'username = ? AND is_active = TRUE',
        [username]
      );

      if (!user) {
        throw new Error('Usuario no encontrado');
      }

      // Verificar contraseña
      const isValidPassword = await this.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        throw new Error('Contraseña incorrecta');
      }

      // Actualizar último login
      await Database.update(
        'admin_users',
        { last_login: new Date() },
        'id = ?',
        [user.id]
      );

      // Generar token
      const token = this.generateToken({
        userId: user.id,
        username: user.username,
        role: user.role
      });

      // Guardar sesión
      const tokenHash = this.hashToken(token);
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 horas

      await Database.saveSession(
        user.id,
        tokenHash,
        expiresAt,
        null, // IP se agregará en el middleware
        null  // User-Agent se agregará en el middleware
      );

      return {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          last_login: user.last_login
        }
      };

    } catch (error) {
      throw error;
    }
  }

  // 🚪 Cerrar sesión
  async logout(token) {
    try {
      const tokenHash = this.hashToken(token);
      await Database.deleteSession(tokenHash);
      return true;
    } catch (error) {
      console.error('Error en logout:', error);
      return false;
    }
  }

  // 🔍 Validar sesión
  async validateSession(token) {
    try {
      // Verificar JWT
      const decoded = this.verifyToken(token);
      
      // Verificar sesión en base de datos
      const tokenHash = this.hashToken(token);
      const session = await Database.getSession(tokenHash);
      
      if (!session) {
        throw new Error('Sesión no encontrada');
      }

      // Buscar usuario actual
      const user = await Database.findOne(
        'admin_users',
        'id = ? AND is_active = TRUE',
        [decoded.userId]
      );

      if (!user) {
        throw new Error('Usuario no encontrado o inactivo');
      }

      return {
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      };

    } catch (error) {
      throw error;
    }
  }

  // 👤 Crear usuario administrador
  async createAdmin(userData) {
    try {
      const { username, email, password, role = 'admin' } = userData;

      // Verificar si ya existe
      const existing = await Database.findOne(
        'admin_users',
        'username = ? OR email = ?',
        [username, email]
      );

      if (existing) {
        throw new Error('Usuario o email ya existe');
      }

      // Hash de contraseña
      const passwordHash = await this.hashPassword(password);

      // Crear usuario
      const userId = await Database.insert('admin_users', {
        username,
        email,
        password_hash: passwordHash,
        role,
        is_active: true
      });

      return {
        id: userId,
        username,
        email,
        role
      };

    } catch (error) {
      throw error;
    }
  }

  // 🔄 Cambiar contraseña
  async changePassword(userId, currentPassword, newPassword) {
    try {
      // Buscar usuario
      const user = await Database.findOne('admin_users', 'id = ?', [userId]);
      if (!user) {
        throw new Error('Usuario no encontrado');
      }

      // Verificar contraseña actual
      const isValidPassword = await this.verifyPassword(currentPassword, user.password_hash);
      if (!isValidPassword) {
        throw new Error('Contraseña actual incorrecta');
      }

      // Hash nueva contraseña
      const newPasswordHash = await this.hashPassword(newPassword);

      // Actualizar contraseña
      await Database.update(
        'admin_users',
        { password_hash: newPasswordHash },
        'id = ?',
        [userId]
      );

      return true;

    } catch (error) {
      throw error;
    }
  }

  // 🧹 Limpiar sesiones expiradas
  async cleanupSessions() {
    try {
      await Database.cleanExpiredSessions();
      console.log('✅ Sesiones expiradas limpiadas');
    } catch (error) {
      console.error('❌ Error limpiando sesiones:', error);
    }
  }

  // 📊 Obtener estadísticas de sesiones
  async getSessionStats() {
    try {
      const totalSessions = await Database.count('user_sessions');
      const activeSessions = await Database.count('user_sessions', 'expires_at > NOW()');
      
      return {
        total: totalSessions,
        active: activeSessions,
        expired: totalSessions - activeSessions
      };
    } catch (error) {
      console.error('Error obteniendo estadísticas de sesiones:', error);
      return { total: 0, active: 0, expired: 0 };
    }
  }
}

module.exports = new AuthService();
