import 'package:flutter/material.dart';
import '../screens/home_screen.dart';
import '../screens/device_suspended_screen.dart';
import '../services/remote_command_service.dart';

/// Wrapper que decide si mostrar el launcher o la pantalla de suspensión
class LauncherWrapper extends StatefulWidget {
  const LauncherWrapper({Key? key}) : super(key: key);

  @override
  State<LauncherWrapper> createState() => _LauncherWrapperState();
}

class _LauncherWrapperState extends State<LauncherWrapper> {
  final RemoteCommandService _commandService = RemoteCommandService();
  bool _isLoading = true;
  bool _isDeviceSuspended = false;

  @override
  void initState() {
    super.initState();
    _checkDeviceStatus();
    _setupCommandListeners();
  }

  /// Verificar estado del dispositivo
  Future<void> _checkDeviceStatus() async {
    try {
      final isSuspended = await _commandService.isDeviceSuspended();
      
      if (mounted) {
        setState(() {
          _isDeviceSuspended = isSuspended;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ Error verificando estado del dispositivo: $e');
      
      if (mounted) {
        setState(() {
          _isDeviceSuspended = false;
          _isLoading = false;
        });
      }
    }
  }

  /// Configurar listeners para comandos remotos
  void _setupCommandListeners() {
    // Listener para suspensión
    _commandService.onSuspendDevice = () {
      if (mounted) {
        setState(() {
          _isDeviceSuspended = true;
        });
      }
    };

    // Listener para activación
    _commandService.onActivateDevice = () {
      if (mounted) {
        // Verificar si estaba suspendido antes de cambiar el estado
        final wasSuspended = _isDeviceSuspended;

        setState(() {
          _isDeviceSuspended = false;
        });

        // Si estaba suspendido, no necesitamos navegar porque el build()
        // automáticamente mostrará HomeScreen en lugar de DeviceSuspendedScreen
        print('✅ Dispositivo activado - Estado suspendido: $wasSuspended → false');
      }
    };

    // Listener para mensajes
    _commandService.onMessageReceived = (message, flash) {
      _showRemoteMessage(message, flash);
    };

    // Listener para cambio de wallpaper
    _commandService.onWallpaperChange = (wallpaperUrl) {
      _handleWallpaperChange(wallpaperUrl);
    };

    // Listener para reinicio
    _commandService.onRebootDevice = () {
      _handleRebootRequest();
    };

    // Listener para sincronización
    _commandService.onSyncRequest = () {
      _handleSyncRequest();
    };
  }

  /// Mostrar mensaje remoto
  void _showRemoteMessage(String message, bool flash) {
    if (!mounted) return;

    if (flash) {
      // Mostrar mensaje con flash (overlay completo)
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.red,
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Text(
                'MENSAJE URGENTE',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.red,
              ),
              child: const Text('ENTENDIDO'),
            ),
          ],
        ),
      );
    } else {
      // Mostrar mensaje normal (SnackBar)
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.message, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      );
    }
  }

  /// Manejar cambio de wallpaper
  void _handleWallpaperChange(String wallpaperUrl) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.wallpaper, color: Colors.white),
            const SizedBox(width: 12),
            const Text('Fondo de pantalla actualizado'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );

    // TODO: Implementar cambio real de wallpaper
    print('🖼️ Cambiar wallpaper a: $wallpaperUrl');
  }

  /// Manejar solicitud de reinicio
  void _handleRebootRequest() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.restart_alt, color: Colors.orange, size: 32),
            SizedBox(width: 12),
            Text('REINICIO SOLICITADO'),
          ],
        ),
        content: const Text(
          'El administrador ha solicitado reiniciar este dispositivo. El reinicio se realizará en 10 segundos.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implementar reinicio real del dispositivo
              print('🔄 Reiniciando dispositivo...');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('REINICIAR AHORA'),
          ),
        ],
      ),
    );
  }

  /// Manejar solicitud de sincronización
  void _handleSyncRequest() {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Sincronizando con el servidor...'),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 3),
      ),
    );

    print('🔄 Sincronización solicitada');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              SizedBox(height: 16),
              Text(
                'Verificando estado del dispositivo...',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    // Si el dispositivo está suspendido, mostrar pantalla de bloqueo
    if (_isDeviceSuspended) {
      return const DeviceSuspendedScreen();
    }

    // Si no está suspendido, mostrar el launcher normal
    return const HomeScreen();
  }
}
