import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Servicio para control del dispositivo (reinicio, etc.)
class DeviceControlService {
  static final DeviceControlService _instance = DeviceControlService._internal();
  factory DeviceControlService() => _instance;
  DeviceControlService._internal();

  static const MethodChannel _channel = MethodChannel('ruralcare/device_control');

  /// Reiniciar el dispositivo
  Future<bool> rebootDevice({int delaySeconds = 5}) async {
    try {
      print('🔄 Reiniciando dispositivo en $delaySeconds segundos...');
      
      // Convertir segundos a milisegundos
      final delayMs = delaySeconds * 1000;
      
      // Llamar al método nativo
      final bool result = await _channel.invokeMethod('rebootDevice', {
        'delay': delayMs,
      });

      if (result) {
        print('✅ Comando de reinicio enviado exitosamente');
      } else {
        print('❌ Error enviando comando de reinicio');
      }

      return result;
    } catch (e) {
      print('❌ Excepción reiniciando dispositivo: $e');
      return false;
    }
  }

  /// Reiniciar dispositivo inmediatamente
  Future<bool> rebootNow() async {
    return await rebootDevice(delaySeconds: 0);
  }

  /// Reiniciar dispositivo con delay personalizado
  Future<bool> rebootWithDelay(int seconds) async {
    if (seconds < 0) {
      print('❌ Delay no puede ser negativo');
      return false;
    }
    
    if (seconds > 300) { // Máximo 5 minutos
      print('❌ Delay máximo es 300 segundos (5 minutos)');
      return false;
    }
    
    return await rebootDevice(delaySeconds: seconds);
  }

  /// Verificar si el dispositivo puede reiniciarse
  Future<bool> canReboot() async {
    try {
      // Intentar llamar al método nativo para verificar permisos
      // Si no hay excepción, significa que el canal está disponible
      await _channel.invokeMethod('checkRebootPermissions');
      return true;
    } catch (e) {
      // Si hay excepción, probablemente no hay permisos o el método no existe
      print('⚠️ No se pueden verificar permisos de reinicio: $e');
      return false;
    }
  }

  /// Obtener información sobre capacidades de reinicio
  Future<Map<String, dynamic>> getRebootCapabilities() async {
    try {
      final Map<dynamic, dynamic> result = await _channel.invokeMethod('getRebootCapabilities');
      return Map<String, dynamic>.from(result);
    } catch (e) {
      print('⚠️ Error obteniendo capacidades de reinicio: $e');
      return {
        'hasDeviceAdmin': false,
        'hasRootAccess': false,
        'canSoftReboot': true,
        'error': e.toString(),
      };
    }
  }

  /// Mostrar diálogo de confirmación de reinicio
  static Future<bool> showRebootConfirmation(
    BuildContext context, {
    int delaySeconds = 5,
    String? customMessage,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.restart_alt, color: Colors.orange, size: 32),
              SizedBox(width: 12),
              Text('REINICIAR DISPOSITIVO'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                customMessage ?? 
                'El administrador ha solicitado reiniciar este dispositivo.',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              if (delaySeconds > 0) ...[
                Text(
                  'El reinicio se realizará en $delaySeconds segundos.',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(height: 8),
              ],
              const Text(
                '⚠️ Asegúrese de guardar cualquier trabajo importante.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.red,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            if (delaySeconds > 0)
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('CANCELAR'),
              ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text(delaySeconds > 0 ? 'REINICIAR AHORA' : 'CONFIRMAR'),
            ),
          ],
        );
      },
    ) ?? false;
  }
}
