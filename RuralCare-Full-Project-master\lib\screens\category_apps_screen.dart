import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../models/app_item.dart';
import '../models/app_category.dart';
import '../constants/app_categories.dart';
import '../services/app_management_service.dart';
import '../services/app_launcher_service.dart';
import '../widgets/sidebar_navigation.dart';
import '../widgets/app_grid_item.dart';

/// Pantalla que muestra las aplicaciones organizadas por categorías
/// Similar al diseño mostrado en la imagen de referencia
class CategoryAppsScreen extends StatefulWidget {
  final NavigationCategory? initialCategory;

  const CategoryAppsScreen({
    super.key,
    this.initialCategory,
  });

  @override
  State<CategoryAppsScreen> createState() => _CategoryAppsScreenState();
}

class _CategoryAppsScreenState extends State<CategoryAppsScreen> {
  late NavigationCategory _selectedCategory;
  List<AppItem> _apps = [];
  bool _isLoading = true;
  List<NavigationCategory> _categories = [];

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory ?? NavigationCategory.favorites;
    _loadCategories();
    _loadApps();
  }

  void _loadCategories() {
    final stats = AppManagementService.instance.getAppStatistics();

    _categories = [
      // Todas las apps
      NavigationCategory(
        id: 'all',
        label: 'Todas',
        icon: Icons.apps,
        color: const Color(0xFF795548),
        appCount: stats['Total'] ?? 0,
      ),

      // Favoritos
      NavigationCategory(
        id: 'favorites',
        label: 'Favoritos',
        icon: Icons.star,
        color: const Color(0xFFFF9800), // Color naranja como en la imagen
        appCount: stats['Favoritos'] ?? 0,
      ),

      // Categorías dinámicas desde AppCategories (excluyendo favoritos para evitar duplicación)
      ...AppCategories.allCategories
          .where((category) => category.id != 'favorites') // Excluir favoritos
          .map((category) => NavigationCategory(
            id: category.id,
            label: category.name,
            icon: category.icon,
            color: category.color,
            appCount: stats[category.name] ?? 0,
          ))
          .where((cat) => cat.appCount > 0), // Solo mostrar categorías con apps
    ];

    setState(() {});
  }

  Future<void> _loadApps() async {
    setState(() => _isLoading = true);
    
    try {
      List<AppItem> apps;
      
      // Filtrar apps según la categoría seleccionada
      if (_selectedCategory.id == 'favorites') {
        apps = AppManagementService.instance.getFavoriteApps();
      } else if (_selectedCategory.id == 'all') {
        apps = AppManagementService.instance.getVisibleApps();
      } else {
        // Filtrar por categoría específica
        final category = AppCategories.getCategoryById(_selectedCategory.id);
        apps = AppManagementService.instance.getAppsByCategory(category);
      }
      
      setState(() {
        _apps = apps;
        _isLoading = false;
      });
    } catch (e) {
      print('Error cargando apps: $e');
      setState(() => _isLoading = false);
    }
  }

  void _onCategorySelected(NavigationCategory category) {
    if (category != _selectedCategory) {
      setState(() => _selectedCategory = category);
      _loadApps();
    }
  }

  Future<void> _handleAppTap(AppItem app) async {
    try {
      await AppLauncherService.instance.launchApp(app.packageName);
    } catch (e) {
      print('Error al abrir la aplicación: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No se pudo abrir ${app.name}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF1976D2), // Azul más intenso
              Color(0xFF2196F3), // Azul medio
              Color(0xFF42A5F5), // Azul más claro
            ],
          ),
        ),
        child: Row(
          children: [
            // Panel izquierdo - Categorías
            _buildCategoriesPanel(),
            
            // Panel derecho - Aplicaciones
            Expanded(
              child: _buildAppsPanel(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesPanel() {
    return Container(
      width: 300,
      decoration: BoxDecoration(
        color: const Color(0xFF0D47A1).withValues(alpha: 0.9),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header del panel
          Container(
            padding: const EdgeInsets.all(24),
            child: const Row(
              children: [
                Icon(
                  Icons.category,
                  color: Colors.white,
                  size: 32,
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Todas las aplicaciones:',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Lista de categorías
          Expanded(
            child: Container(
              color: Colors.black.withValues(alpha: 0.2),
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return _buildCategoryItem(category);
                },
              ),
            ),
          ),

          // Botón volver
          Container(
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF1976D2),
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  elevation: 4,
                  shadowColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back, size: 24),
                    SizedBox(width: 8),
                    Text(
                      'VOLVER',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(NavigationCategory category) {
    final isSelected = category.id == _selectedCategory.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: isSelected ? category.color.withValues(alpha: 0.2) : Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        elevation: isSelected ? 4 : 2,
        child: InkWell(
          onTap: () => _onCategorySelected(category),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icono de la categoría
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: category.color,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: category.color.withValues(alpha: 0.4),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 28,
                  ),
                ),

                const SizedBox(width: 16),

                // Nombre y contador
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.label,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      if (category.appCount > 0)
                        Text(
                          '${category.appCount} apps',
                          style: TextStyle(
                            fontSize: 14,
                            color: isSelected ? Colors.white.withValues(alpha: 0.8) : Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                    ],
                  ),
                ),

                // Indicador de selección
                if (isSelected)
                  Container(
                    width: 4,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppsPanel() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header del panel de aplicaciones
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _selectedCategory.color,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: _selectedCategory.color.withValues(alpha: 0.4),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  _selectedCategory.icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedCategory.label,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (_apps.isNotEmpty)
                      Text(
                        '${_apps.length} aplicaciones',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Lista de aplicaciones
          Expanded(
            child: _buildAppsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAppsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (_apps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedCategory.icon,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No hay aplicaciones en esta categoría',
              style: TextStyle(
                fontSize: 18,
                color: Colors.white.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _apps.length,
      itemBuilder: (context, index) {
        final app = _apps[index];
        return _buildAppListItem(app);
      },
    );
  }

  Widget _buildAppListItem(AppItem app) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        elevation: 2,
        child: InkWell(
          onTap: () => _handleAppTap(app),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Icono de la aplicación
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: app.appIcon != null
                        ? Image.memory(
                            app.appIcon!,
                            width: 56,
                            height: 56,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                app.icon,
                                size: 32,
                                color: Colors.white,
                              );
                            },
                          )
                        : Icon(
                            app.icon,
                            size: 32,
                            color: Colors.white,
                          ),
                  ),
                ),

                const SizedBox(width: 16),

                // Nombre de la aplicación
                Expanded(
                  child: Text(
                    app.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),

                // Indicador de favorito
                if (app.isFavorite)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFD700).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.star,
                      color: Color(0xFFFFD700),
                      size: 20,
                    ),
                  ),

                const SizedBox(width: 8),

                // Flecha indicadora
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.6),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
