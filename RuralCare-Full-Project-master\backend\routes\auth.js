const express = require('express');
const AuthService = require('../services/AuthService');
const router = express.Router();

// 🔐 Login
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        error: 'Usuario y contraseña son requeridos'
      });
    }
    
    const result = await AuthService.authenticateUser(username, password);
    
    res.json({
      success: true,
      message: 'Login exitoso',
      data: result
    });
    
  } catch (error) {
    res.status(401).json({
      error: error.message
    });
  }
});

// 🚪 Logout
router.post('/logout', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      await AuthService.logout(token);
    }
    
    res.json({
      success: true,
      message: 'Logout exitoso'
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Error en logout'
    });
  }
});

// 👤 Obtener información del usuario actual
router.get('/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        error: 'Token requerido'
      });
    }
    
    const user = await AuthService.validateSession(token);
    
    res.json({
      success: true,
      data: user
    });
    
  } catch (error) {
    res.status(401).json({
      error: error.message
    });
  }
});

module.exports = router;
