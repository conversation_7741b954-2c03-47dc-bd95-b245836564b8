package com.ruralcare.ruralcare

import android.app.admin.DeviceAdminReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * Device Admin Receiver para RuralCare
 * Permite activar el modo kiosko y otras funcionalidades de administración
 */
class RuralCareDeviceAdminReceiver : DeviceAdminReceiver() {
    
    companion object {
        private const val TAG = "RuralCareDeviceAdmin"
    }

    override fun onEnabled(context: Context, intent: Intent) {
        super.onEnabled(context, intent)
        Log.d(TAG, "Device Admin habilitado para RuralCare")
    }

    override fun onDisabled(context: Context, intent: Intent) {
        super.onDisabled(context, intent)
        Log.d(TAG, "Device Admin deshabilitado para RuralCare")
    }

    override fun onLockTaskModeEntering(context: Context, intent: Intent, pkg: String) {
        super.onLockTaskModeEntering(context, intent, pkg)
        Log.d(TAG, "Entrando en modo Lock Task para: $pkg")
    }

    override fun onLockTaskModeExiting(context: Context, intent: Intent) {
        super.onLockTaskModeExiting(context, intent)
        Log.d(TAG, "Saliendo del modo Lock Task")
    }
}
