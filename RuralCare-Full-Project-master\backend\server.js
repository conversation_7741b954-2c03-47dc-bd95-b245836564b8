const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// Importar servicios
const Database = require('./services/Database');
const AuthService = require('./services/AuthService');

// Importar rutas
const authRoutes = require('./routes/auth');
const deviceRoutes = require('./routes/devices');
const adminRoutes = require('./routes/admin');
const apiRoutes = require('./routes/api');

const app = express();
const PORT = process.env.PORT || 3000;

// 🔐 Configuración de seguridad
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// 🌐 Configuración CORS simplificada para desarrollo
const corsOptions = {
  origin: true, // Permitir todos los orígenes en desarrollo
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
};

app.use(cors(corsOptions));

// 📊 Logging
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

// 🚦 Rate limiting básico
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    error: 'Demasiadas peticiones desde esta IP, intenta de nuevo más tarde.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// 📝 Parsing de body
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 📁 Servir archivos estáticos
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 🏥 Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// 📡 Rutas de la API
app.use('/api/auth', authRoutes);
app.use('/api/devices', deviceRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api', apiRoutes);

// 📊 Documentación de la API
app.get('/api', (req, res) => {
  res.json({
    name: 'RuralCare Backend API',
    version: '1.0.0',
    description: 'API para gestión remota de tablets RuralCare - Optimizado para BanaHosting',
    endpoints: {
      health: 'GET /health',
      auth: {
        login: 'POST /api/auth/login',
        logout: 'POST /api/auth/logout',
        me: 'GET /api/auth/me'
      },
      devices: {
        register: 'POST /api/devices/register',
        heartbeat: 'POST /api/devices/heartbeat',
        commands: 'GET /api/devices/commands',
        ack: 'POST /api/devices/command-ack'
      },
      admin: {
        devices: 'GET /api/admin/devices',
        suspend: 'POST /api/admin/devices/:id/suspend',
        activate: 'POST /api/admin/devices/:id/activate',
        sendCommand: 'POST /api/admin/devices/:id/command',
        logs: 'GET /api/admin/logs',
        stats: 'GET /api/admin/stats'
      }
    },
    database: 'MySQL (sin Redis)',
    hosting: 'Optimizado para BanaHosting'
  });
});

// 🚫 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint no encontrado',
    path: req.originalUrl,
    method: req.method
  });
});

// 🚨 Error handler global
app.use((error, req, res, next) => {
  console.error('Error:', error);
  
  // Error de CORS
  if (error.message === 'No permitido por CORS') {
    return res.status(403).json({
      error: 'CORS: Origen no permitido',
      origin: req.get('Origin')
    });
  }
  
  // Error de validación
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Error de validación',
      details: error.message
    });
  }
  
  // Error de JWT
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: 'Token inválido'
    });
  }
  
  // Error genérico
  res.status(500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Error interno del servidor' 
      : error.message
  });
});

// 🚀 Iniciar servidor
async function startServer() {
  try {
    // Conectar a la base de datos
    console.log('🔌 Conectando a MySQL...');
    await Database.connect();
    console.log('✅ MySQL conectado correctamente');
    
    // Iniciar servidor HTTP en todas las interfaces
    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log(`🚀 Servidor ejecutándose en puerto ${PORT}`);
      console.log(`📊 Entorno: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🌐 API URL Local: http://localhost:${PORT}/api`);
      console.log(`🌐 API URL Red: http://*************:${PORT}/api`);
      console.log(`🏥 Health check: http://localhost:${PORT}/health`);
      console.log(`📖 Documentación: http://localhost:${PORT}/api`);
    });
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('🛑 SIGTERM recibido, cerrando servidor...');
      server.close(() => {
        console.log('✅ Servidor HTTP cerrado');
        Database.close();
        process.exit(0);
      });
    });
    
    process.on('SIGINT', () => {
      console.log('🛑 SIGINT recibido, cerrando servidor...');
      server.close(() => {
        console.log('✅ Servidor HTTP cerrado');
        Database.close();
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Error iniciando servidor:', error);
    process.exit(1);
  }
}

// Manejar errores no capturados
process.on('uncaughtException', (error) => {
  console.error('💥 Excepción no capturada:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Promesa rechazada no manejada:', promise, 'razón:', reason);
  process.exit(1);
});

// Iniciar el servidor
startServer();

module.exports = app;
