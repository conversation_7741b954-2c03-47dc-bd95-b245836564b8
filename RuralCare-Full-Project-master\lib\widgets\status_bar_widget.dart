import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';

/// Barra de estado personalizada que muestra información importante
/// de manera clara y accesible para personas mayores
class StatusBarWidget extends StatefulWidget {
  const StatusBarWidget({super.key});

  @override
  State<StatusBarWidget> createState() => _StatusBarWidgetState();
}

class _StatusBarWidgetState extends State<StatusBarWidget> {
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  late Stream<List<ConnectivityResult>> _connectivityStream;

  @override
  void initState() {
    super.initState();
    _initConnectivity();
    _connectivityStream = Connectivity().onConnectivityChanged;
    _connectivityStream.listen(_updateConnectionStatus);
  }

  Future<void> _initConnectivity() async {
    try {
      final result = await Connectivity().checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      _updateConnectionStatus([ConnectivityResult.none]);
    }
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    if (mounted) {
      setState(() {
        _connectionStatus = results;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final isLargeTablet = screenWidth > 900;

    // Ajustar padding según el tamaño de pantalla
    final horizontalPadding = isLargeTablet
        ? AppDimensions.paddingXL
        : isTablet
            ? AppDimensions.paddingL
            : AppDimensions.paddingM;

    return Container(
      height: AppDimensions.statusBarHeight,
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        gradient: AppColors.surfaceGradient,
        border: Border(
          bottom: BorderSide(
            color: AppColors.accent.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Hora actual - Flexible para adaptarse
              Flexible(
                flex: 2,
                child: _buildTimeWidget(isTablet),
              ),

              // Espaciador para centrar mejor
              if (constraints.maxWidth > 400) const Spacer(),

              // Indicadores de estado - Tamaño fijo pero responsivo
              _buildStatusIndicators(isTablet, isLargeTablet),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTimeWidget([bool isTablet = false]) {
    return StreamBuilder(
      stream: Stream.periodic(const Duration(seconds: 1)),
      builder: (context, snapshot) {
        final now = DateTime.now();
        final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

        return Text(
          timeString,
          style: TextStyle(
            fontSize: isTablet ? AppDimensions.fontL : AppDimensions.fontM,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
            shadows: const [
              Shadow(
                color: Colors.black26,
                offset: Offset(0, 1),
                blurRadius: 2,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusIndicators(bool isTablet, bool isLargeTablet) {
    // Ajustar espaciado según el tamaño de pantalla
    final spacing = isLargeTablet
        ? AppDimensions.paddingM
        : isTablet
            ? AppDimensions.paddingS
            : AppDimensions.paddingXS;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildConnectionIndicator(isTablet),
        SizedBox(width: spacing),
        _buildBatteryIndicator(isTablet),
      ],
    );
  }

  Widget _buildConnectionIndicator([bool isTablet = false]) {
    IconData icon;
    Color color;
    String tooltip;

    // Tomar el primer resultado de conectividad
    final primaryConnection = _connectionStatus.isNotEmpty
        ? _connectionStatus.first
        : ConnectivityResult.none;

    switch (primaryConnection) {
      case ConnectivityResult.wifi:
        icon = Icons.wifi;
        color = AppColors.connected;
        tooltip = 'WiFi conectado';
        break;
      case ConnectivityResult.mobile:
        icon = Icons.signal_cellular_4_bar;
        color = AppColors.connected;
        tooltip = 'Datos móviles conectados';
        break;
      case ConnectivityResult.ethernet:
        icon = Icons.lan;
        color = AppColors.connected;
        tooltip = 'Ethernet conectado';
        break;
      default:
        icon = Icons.wifi_off;
        color = AppColors.disconnected;
        tooltip = 'Sin conexión';
    }

    // Ajustar tamaño según el dispositivo
    final iconSize = isTablet ? AppDimensions.iconL : AppDimensions.iconM;
    final padding = isTablet ? AppDimensions.paddingM : AppDimensions.paddingS;

    return Tooltip(
      message: tooltip,
      child: Container(
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.2),
              color.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          size: iconSize,
          color: color,
        ),
      ),
    );
  }

  Widget _buildBatteryIndicator([bool isTablet = false]) {
    // Por ahora mostramos un indicador estático
    // TODO: Implementar lectura real del nivel de batería

    // Ajustar tamaños según el dispositivo
    final iconSize = isTablet ? AppDimensions.iconL : AppDimensions.iconM;
    final fontSize = isTablet ? AppDimensions.fontM : AppDimensions.fontS;
    final padding = isTablet ? AppDimensions.paddingM : AppDimensions.paddingS;
    final spacing = isTablet ? AppDimensions.paddingS : AppDimensions.paddingXS;

    return Tooltip(
      message: 'Batería: 85%',
      child: Container(
        padding: EdgeInsets.all(padding),
        constraints: BoxConstraints(
          minWidth: isTablet ? 80 : 60,
          maxWidth: isTablet ? 120 : 100,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.success.withValues(alpha: 0.2),
              AppColors.success.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: AppColors.success.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.success.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.battery_4_bar,
              size: iconSize,
              color: AppColors.success,
            ),
            SizedBox(width: spacing),
            Flexible(
              child: Text(
                '85%',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }


}
