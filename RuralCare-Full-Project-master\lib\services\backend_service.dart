import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../config/backend_config.dart';

/// Servicio para comunicación con el backend
class BackendService {
  static final BackendService _instance = BackendService._internal();
  factory BackendService() => _instance;
  BackendService._internal();

  // Configuración del servidor
  static final String _baseUrl = BackendConfig.getBaseUrl();
  static final Duration _timeout = BackendConfig.connectionTimeout;
  
  // Cliente HTTP
  late http.Client _client;
  String? _deviceId;
  String? _deviceUuid;

  /// Inicializar el servicio
  Future<void> initialize() async {
    _client = http.Client();
    await _generateDeviceId();
    print('🔌 BackendService inicializado');
    print('📱 Device ID: $_deviceId');
  }

  /// Generar ID único del dispositivo
  Future<void> _generateDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Intentar obtener ID existente
    _deviceUuid = prefs.getString('device_uuid');
    
    if (_deviceUuid == null) {
      // Generar nuevo UUID
      _deviceUuid = const Uuid().v4();
      await prefs.setString('device_uuid', _deviceUuid!);
      print('🆔 Nuevo Device UUID generado: $_deviceUuid');
    }

    // Crear device_id legible
    final deviceInfo = DeviceInfoPlugin();
    try {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = '${androidInfo.brand}-${androidInfo.model}-${_deviceUuid!.substring(0, 8)}';
    } catch (e) {
      _deviceId = 'unknown-device-${_deviceUuid!.substring(0, 8)}';
    }
  }

  /// Obtener información del dispositivo
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    
    try {
      final androidInfo = await deviceInfo.androidInfo;
      
      return {
        'device_id': _deviceId,
        'device_name': '${androidInfo.brand} ${androidInfo.model}',
        'launcher_version': '1.0.0',
        'android_version': androidInfo.version.release,
        'model': androidInfo.model,
        'manufacturer': androidInfo.manufacturer,
        'brand': androidInfo.brand,
        'sdk_int': androidInfo.version.sdkInt,
        'device_info': {
          'board': androidInfo.board,
          'bootloader': androidInfo.bootloader,
          'device': androidInfo.device,
          'display': androidInfo.display,
          'fingerprint': androidInfo.fingerprint,
          'hardware': androidInfo.hardware,
          'host': androidInfo.host,
          'id': androidInfo.id,
          'product': androidInfo.product,
          'supported32BitAbis': androidInfo.supported32BitAbis,
          'supported64BitAbis': androidInfo.supported64BitAbis,
          'supportedAbis': androidInfo.supportedAbis,
          'tags': androidInfo.tags,
          'type': androidInfo.type,
          'isPhysicalDevice': androidInfo.isPhysicalDevice,
        }
      };
    } catch (e) {
      print('❌ Error obteniendo info del dispositivo: $e');
      return {
        'device_id': _deviceId,
        'device_name': 'Dispositivo Desconocido',
        'launcher_version': '1.0.0',
        'android_version': 'Unknown',
        'model': 'Unknown',
        'manufacturer': 'Unknown',
        'brand': 'Unknown',
        'sdk_int': 0,
        'device_info': {}
      };
    }
  }

  /// Realizar petición HTTP
  Future<Map<String, dynamic>> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl$endpoint');
      final defaultHeaders = {
        'Content-Type': 'application/json',
        'User-Agent': 'SeniorLauncher/1.0.0',
      };
      
      if (headers != null) {
        defaultHeaders.addAll(headers);
      }

      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client.get(url, headers: defaultHeaders).timeout(_timeout);
          break;
        case 'POST':
          response = await _client.post(
            url,
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(_timeout);
          break;
        case 'PUT':
          response = await _client.put(
            url,
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(_timeout);
          break;
        case 'DELETE':
          response = await _client.delete(url, headers: defaultHeaders).timeout(_timeout);
          break;
        default:
          throw Exception('Método HTTP no soportado: $method');
      }

      print('📡 ${method.toUpperCase()} $endpoint - Status: ${response.statusCode}');
      
      if (response.body.isNotEmpty) {
        final data = jsonDecode(response.body);
        return {
          'success': response.statusCode >= 200 && response.statusCode < 300,
          'statusCode': response.statusCode,
          'data': data,
        };
      } else {
        return {
          'success': response.statusCode >= 200 && response.statusCode < 300,
          'statusCode': response.statusCode,
          'data': null,
        };
      }
      
    } catch (e) {
      print('❌ Error en petición $method $endpoint: $e');
      return {
        'success': false,
        'error': e.toString(),
        'statusCode': 0,
      };
    }
  }

  /// Registrar dispositivo en el backend
  Future<bool> registerDevice() async {
    try {
      print('📱 Registrando dispositivo...');
      
      final deviceInfo = await _getDeviceInfo();
      final result = await _makeRequest('POST', '/devices/register', body: deviceInfo);
      
      if (result['success']) {
        print('✅ Dispositivo registrado exitosamente');
        return true;
      } else {
        print('❌ Error registrando dispositivo: ${result['data']}');
        return false;
      }
    } catch (e) {
      print('❌ Excepción registrando dispositivo: $e');
      return false;
    }
  }

  /// Enviar heartbeat al backend
  Future<bool> sendHeartbeat() async {
    try {
      final result = await _makeRequest('POST', '/devices/heartbeat', body: {
        'device_id': _deviceId,
      });
      
      if (result['success']) {
        print('💓 Heartbeat enviado');
        return true;
      } else {
        print('❌ Error enviando heartbeat: ${result['data']}');
        return false;
      }
    } catch (e) {
      print('❌ Excepción enviando heartbeat: $e');
      return false;
    }
  }

  /// Obtener comandos pendientes
  Future<List<Map<String, dynamic>>> getCommands() async {
    try {
      final result = await _makeRequest('GET', '/devices/commands?device_id=$_deviceId');
      
      if (result['success']) {
        final data = result['data'];
        if (data != null && data['commands'] != null) {
          return List<Map<String, dynamic>>.from(data['commands']);
        }
      }
      
      return [];
    } catch (e) {
      print('❌ Error obteniendo comandos: $e');
      return [];
    }
  }

  /// Confirmar ejecución de comando
  Future<bool> acknowledgeCommand(String commandId, String status, {String? errorMessage}) async {
    try {
      final result = await _makeRequest('POST', '/devices/command-ack', body: {
        'command_id': commandId,
        'status': status,
        'error_message': errorMessage,
      });
      
      return result['success'];
    } catch (e) {
      print('❌ Error confirmando comando: $e');
      return false;
    }
  }

  /// Verificar conectividad con el backend
  Future<bool> checkConnectivity() async {
    try {
      final result = await _makeRequest('GET', '/test');
      return result['success'];
    } catch (e) {
      print('❌ Sin conectividad con backend: $e');
      return false;
    }
  }

  /// Obtener ID del dispositivo
  String? get deviceId => _deviceId;

  /// Cerrar cliente HTTP
  void dispose() {
    _client.close();
  }
}
