{"name": "ruralcare-backend", "version": "1.0.0", "description": "Backend para RuralCare - Optimizado para BanaHosting", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ruralcare", "tablet-management", "mysql", "express"], "author": "<PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}