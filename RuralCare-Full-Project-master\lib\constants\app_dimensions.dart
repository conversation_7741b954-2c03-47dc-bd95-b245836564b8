/// Dimensiones y espaciados para el launcher orientado a personas mayores
/// Todas las dimensiones están optimizadas para facilitar la interacción
class AppDimensions {
  // Espaciados generales
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;
  
  // Márgenes
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  
  // Tamaños de botones - Grandes para facilitar el toque
  static const double buttonHeight = 80.0;
  static const double buttonHeightLarge = 100.0;
  static const double buttonWidth = 200.0;
  static const double buttonWidthLarge = 250.0;
  
  // Tamaños de iconos - Grandes y visibles
  static const double iconXS = 16.0;
  static const double iconS = 24.0;
  static const double iconM = 32.0;
  static const double iconL = 48.0;
  static const double iconXL = 64.0;
  static const double iconXXL = 80.0;
  
  // Tamaños de fuente - Grandes para legibilidad
  static const double fontXS = 12.0;
  static const double fontS = 16.0;
  static const double fontM = 20.0;
  static const double fontL = 24.0;
  static const double fontXL = 28.0;
  static const double fontXXL = 32.0;
  static const double fontTitle = 36.0;
  
  // Radios de borde - Suaves y amigables
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusCircle = 50.0;
  
  // Elevaciones para sombras
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 12.0;
  
  // Dimensiones específicas del launcher
  static const double appIconSize = 72.0;
  static const double appIconSizeLarge = 96.0;
  static const double appCardHeight = 120.0;
  static const double appCardWidth = 120.0;
  
  // Dimensiones de la grilla de aplicaciones
  static const double gridSpacing = 16.0;
  static const int gridCrossAxisCount = 4; // Para tablets
  static const double gridChildAspectRatio = 1.0;
  
  // Dimensiones del panel de control
  static const double controlPanelHeight = 100.0;
  static const double controlButtonSize = 60.0;
  
  // Dimensiones para el modo administrador
  static const double adminButtonHeight = 50.0;
  static const double adminIconSize = 24.0;
  
  // Breakpoints para responsive design
  static const double tabletBreakpoint = 600.0;
  static const double desktopBreakpoint = 1200.0;
  
  // Dimensiones mínimas de toque (accesibilidad)
  static const double minTouchTarget = 48.0;
  static const double recommendedTouchTarget = 60.0;
  
  // Espaciado entre elementos de lista
  static const double listItemSpacing = 12.0;
  static const double listItemHeight = 80.0;
  
  // Dimensiones de diálogos
  static const double dialogMaxWidth = 400.0;
  static const double dialogMinHeight = 200.0;
  
  // Dimensiones de la barra de estado personalizada
  static const double statusBarHeight = 40.0;
  static const double quickSettingsHeight = 55.0;
}
