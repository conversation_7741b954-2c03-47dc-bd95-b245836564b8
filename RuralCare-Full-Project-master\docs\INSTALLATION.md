# 📱 RuralCare - Guía de Instalación

## 🎯 Descripción General

RuralCare es un launcher Android diseñado específicamente para personas mayores en zonas rurales, con interfaz simplificada, botones grandes y control remoto desde un panel web administrativo.

## 📋 Requisitos del Sistema

### **Dispositivo Android (Tablet/Teléfono):**
- Android 7.0 (API 24) o superior
- 2GB RAM mínimo (4GB recomendado)
- 500MB espacio libre
- Conexión a Internet (WiFi recomendado)
- Pantalla de 8+ pulgadas (recomendado para seniors)

### **Servidor Backend:**
- Node.js 16+ 
- MySQL 8.0+
- 1GB RAM mínimo
- Conexión a Internet estable

## 🚀 Instalación Paso a Paso

### **PASO 1: Preparar el Servidor**

#### **1.1 Instalar Dependencias**
```bash
# Instalar Node.js y npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar MySQL
sudo apt-get install mysql-server

# Verificar instalaciones
node --version
npm --version
mysql --version
```

#### **1.2 Configurar Base de Datos**
```bash
# Acceder a MySQL
sudo mysql -u root -p

# Crear base de datos
CREATE DATABASE ruralcare;
CREATE USER 'launcher_user'@'localhost' IDENTIFIED BY 'tu_password_segura';
GRANT ALL PRIVILEGES ON ruralcare.* TO 'launcher_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### **1.3 Instalar Backend**
```bash
# Clonar o copiar archivos del backend
cd /opt/
sudo mkdir ruralcare
sudo chown $USER:$USER ruralcare
cd ruralcare

# Copiar archivos del backend aquí
# (backend/, web-panel/, package.json, etc.)

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
nano .env
```

#### **1.4 Configurar .env**
```env
# 🌐 Configuración del Servidor
PORT=3000
NODE_ENV=production

# 🗄️ Base de Datos
DB_HOST=localhost
DB_PORT=3306
DB_USER=launcher_user
DB_PASSWORD=tu_password_segura
DB_NAME=ruralcare

# 🔐 JWT
JWT_SECRET=tu_jwt_secret_muy_seguro_aqui

# 🌐 CORS (dominios permitidos)
CORS_ORIGINS=https://tu-dominio.com,https://panel.tu-dominio.com

# 📧 Email (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=tu-app-password
```

#### **1.5 Inicializar Base de Datos**
```bash
# Ejecutar migraciones
npm run migrate

# Crear usuario administrador
npm run seed
```

#### **1.6 Instalar Panel Web**
```bash
cd web-panel
npm install
npm run build
cd ..
```

#### **1.7 Configurar Servicio Systemd**
```bash
# Crear archivo de servicio
sudo nano /etc/systemd/system/ruralcare.service
```

```ini
[Unit]
Description=RuralCare Backend
After=network.target mysql.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/ruralcare
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
```

```bash
# Habilitar y iniciar servicio
sudo systemctl enable senior-launcher
sudo systemctl start senior-launcher
sudo systemctl status senior-launcher
```

### **PASO 2: Configurar Nginx (Opcional pero Recomendado)**

#### **2.1 Instalar Nginx**
```bash
sudo apt-get install nginx
```

#### **2.2 Configurar Virtual Host**
```bash
sudo nano /etc/nginx/sites-available/senior-launcher
```

```nginx
server {
    listen 80;
    server_name tu-dominio.com;
    
    # Redirigir HTTP a HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name tu-dominio.com;
    
    # Certificados SSL
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Configuración SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # API Backend
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Panel Web
    location / {
        root /opt/senior-launcher/web-panel/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
}
```

```bash
# Habilitar sitio
sudo ln -s /etc/nginx/sites-available/ruralcare /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### **PASO 3: Instalar APK en Dispositivos Android**

#### **3.1 Preparar APK**
```bash
# En tu máquina de desarrollo
cd ruralcare-flutter
flutter build apk --release --build-name=1.0.0 --build-number=1

# El APK estará en: build/app/outputs/flutter-apk/app-release.apk
```

#### **3.2 Instalar en Dispositivo**
1. **Habilitar "Fuentes desconocidas"** en Configuración > Seguridad
2. **Transferir APK** al dispositivo (USB, email, etc.)
3. **Instalar APK** tocando el archivo
4. **Configurar como launcher por defecto** cuando se solicite

#### **3.3 Configurar Permisos**
1. Ir a **Configuración > Apps > RuralCare > Permisos**
2. Habilitar todos los permisos solicitados:
   - Almacenamiento
   - Ubicación (opcional)
   - Teléfono (opcional)
   - Cámara (opcional)

#### **3.4 Configurar Kiosk Mode (Opcional)**
1. Ir a **Configuración > Seguridad > Administradores de dispositivo**
2. Habilitar **RuralCare** como administrador
3. Esto permite el modo kiosk completo

## ⚙️ Configuración Inicial

### **PASO 4: Configurar Panel Web**

1. **Acceder al panel**: https://tu-dominio.com
2. **Login inicial**:
   - Usuario: `admin`
   - Contraseña: `admin123`
3. **Cambiar contraseña** inmediatamente
4. **Verificar conectividad** con dispositivos

### **PASO 5: Configurar Dispositivos**

1. **Abrir RuralCare** en el dispositivo
2. **Ir a Configuración** (panel de administrador)
3. **Configurar servidor**:
   - URL: `https://tu-dominio.com/api`
   - Verificar conexión
4. **El dispositivo aparecerá** en el panel web automáticamente

## 🔧 Configuración Avanzada

### **Personalización de Temas**
- Editar `lib/constants/app_colors.dart`
- Recompilar APK

### **Configuración de Apps**
- Las apps se categorizan automáticamente
- Personalizar en `lib/constants/app_categories.dart`

### **Configuración de Comandos**
- Todos los comandos remotos están habilitados por defecto
- Personalizar en el panel web

## 🆘 Solución de Problemas

### **Dispositivo no aparece en panel web**
1. Verificar conexión a Internet
2. Verificar URL del servidor en configuración
3. Revisar logs del backend: `sudo journalctl -u ruralcare -f`

### **Comandos remotos no funcionan**
1. Verificar que el dispositivo esté "Activo" en el panel
2. Esperar hasta 30 segundos (polling interval)
3. Verificar permisos de administrador del dispositivo

### **Panel web no carga**
1. Verificar que Nginx esté funcionando: `sudo systemctl status nginx`
2. Verificar que el backend esté funcionando: `sudo systemctl status ruralcare`
3. Revisar logs de Nginx: `sudo tail -f /var/log/nginx/error.log`

## 📞 Soporte

Para soporte técnico:
- Email: <EMAIL>
- Documentación: https://docs.tu-dominio.com
- GitHub: https://github.com/tu-usuario/ruralcare

## 📄 Licencia

Este software está licenciado bajo [Tu Licencia]. Ver archivo LICENSE para más detalles.
