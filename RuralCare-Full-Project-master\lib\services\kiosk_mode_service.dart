import 'package:flutter/services.dart';

/// Servicio para manejar el modo kiosko (Lock Task Mode)
/// Previene salidas involuntarias de la aplicación
class KioskModeService {
  static const MethodChannel _channel = MethodChannel('ruralcare/kiosk_mode');
  
  static KioskModeService? _instance;
  static KioskModeService get instance => _instance ??= KioskModeService._();
  
  KioskModeService._();

  /// Verifica si el Device Admin está habilitado
  Future<bool> isDeviceAdminEnabled() async {
    try {
      final result = await _channel.invokeMethod('isDeviceAdminEnabled');
      return result == true;
    } catch (e) {
      print('Error verificando Device Admin: $e');
      return false;
    }
  }

  /// Solicita permisos de Device Admin
  Future<bool> requestDeviceAdminPermission() async {
    try {
      final result = await _channel.invokeMethod('requestDeviceAdminPermission');
      return result == true;
    } catch (e) {
      print('Error solicitando permisos de Device Admin: $e');
      return false;
    }
  }

  /// Activa el modo kiosko (Lock Task Mode)
  Future<bool> enableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('enableKioskMode');
      return result == true;
    } catch (e) {
      print('Error activando modo kiosko: $e');
      return false;
    }
  }

  /// Desactiva el modo kiosko (solo desde modo administrador)
  Future<bool> disableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('disableKioskMode');
      return result == true;
    } catch (e) {
      print('Error desactivando modo kiosko: $e');
      return false;
    }
  }

  /// Verifica si el modo kiosko está activo
  Future<bool> isKioskModeActive() async {
    try {
      final result = await _channel.invokeMethod('isKioskModeActive');
      return result == true;
    } catch (e) {
      print('Error verificando modo kiosko: $e');
      return false;
    }
  }

  /// Establece la app como launcher por defecto
  Future<bool> setAsDefaultLauncher() async {
    try {
      final result = await _channel.invokeMethod('setAsDefaultLauncher');
      return result == true;
    } catch (e) {
      print('Error estableciendo como launcher por defecto: $e');
      return false;
    }
  }

  /// Verifica si la app es el launcher por defecto
  Future<bool> isDefaultLauncher() async {
    try {
      final result = await _channel.invokeMethod('isDefaultLauncher');
      return result == true;
    } catch (e) {
      print('Error verificando launcher por defecto: $e');
      return false;
    }
  }

  /// Bloquea el botón de inicio (requiere permisos especiales)
  Future<bool> disableHomeButton() async {
    try {
      final result = await _channel.invokeMethod('disableHomeButton');
      return result == true;
    } catch (e) {
      print('Error bloqueando botón de inicio: $e');
      return false;
    }
  }

  /// Habilita el botón de inicio
  Future<bool> enableHomeButton() async {
    try {
      final result = await _channel.invokeMethod('enableHomeButton');
      return result == true;
    } catch (e) {
      print('Error habilitando botón de inicio: $e');
      return false;
    }
  }

  /// Bloquea la barra de notificaciones
  Future<bool> disableStatusBar() async {
    try {
      final result = await _channel.invokeMethod('disableStatusBar');
      return result == true;
    } catch (e) {
      print('Error bloqueando barra de notificaciones: $e');
      return false;
    }
  }

  /// Habilita la barra de notificaciones
  Future<bool> enableStatusBar() async {
    try {
      final result = await _channel.invokeMethod('enableStatusBar');
      return result == true;
    } catch (e) {
      print('Error habilitando barra de notificaciones: $e');
      return false;
    }
  }

  /// Obtiene la lista de apps permitidas configuradas
  Future<List<String>> getAllowedApps() async {
    try {
      final result = await _channel.invokeMethod('getAllowedApps');
      return List<String>.from(result ?? []);
    } catch (e) {
      print('Error obteniendo apps permitidas: $e');
      return [];
    }
  }

  /// Verifica si una app específica está en la lista blanca
  Future<bool> isAppAllowed(String packageName) async {
    try {
      final result = await _channel.invokeMethod('isAppAllowed', {'packageName': packageName});
      return result == true;
    } catch (e) {
      print('Error verificando app permitida: $e');
      return false;
    }
  }

  /// Verifica si la app es Device Owner (permisos completos)
  Future<bool> isDeviceOwner() async {
    try {
      final result = await _channel.invokeMethod('isDeviceOwner');
      return result == true;
    } catch (e) {
      print('Error verificando Device Owner: $e');
      return false;
    }
  }

  /// Configuración completa del modo kiosko
  Future<bool> setupKioskMode({
    bool blockHomeButton = true,
    bool blockStatusBar = true,
    bool setAsDefaultLauncher = true,
  }) async {
    try {
      // 1. Verificar y solicitar permisos de Device Admin
      if (!await isDeviceAdminEnabled()) {
        final adminGranted = await requestDeviceAdminPermission();
        if (!adminGranted) {
          print('Permisos de Device Admin no concedidos');
          return false;
        }
      }

      // 2. Establecer como launcher por defecto
      if (setAsDefaultLauncher) {
        await this.setAsDefaultLauncher();
      }

      // 3. Activar modo kiosko
      final kioskEnabled = await enableKioskMode();
      if (!kioskEnabled) {
        print('No se pudo activar el modo kiosko');
        return false;
      }

      // 4. Bloquear controles del sistema si se solicita
      if (blockHomeButton) {
        await disableHomeButton();
      }

      if (blockStatusBar) {
        await disableStatusBar();
      }

      return true;
    } catch (e) {
      print('Error configurando modo kiosko: $e');
      return false;
    }
  }

  /// Desactivar completamente el modo kiosko
  Future<bool> exitKioskMode() async {
    try {
      // 1. Habilitar controles del sistema
      await enableHomeButton();
      await enableStatusBar();

      // 2. Desactivar modo kiosko
      final kioskDisabled = await disableKioskMode();
      
      return kioskDisabled;
    } catch (e) {
      print('Error saliendo del modo kiosko: $e');
      return false;
    }
  }
}
