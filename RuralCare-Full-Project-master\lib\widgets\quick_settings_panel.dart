import 'package:flutter/material.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:volume_controller/volume_controller.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_strings.dart';

/// Panel de configuraciones rápidas para ajustes básicos del sistema
/// Diseñado para ser fácil de usar por personas mayores
class QuickSettingsPanel extends StatefulWidget {
  const QuickSettingsPanel({super.key});

  @override
  State<QuickSettingsPanel> createState() => _QuickSettingsPanelState();
}

class _QuickSettingsPanelState extends State<QuickSettingsPanel> {
  double _currentVolume = 0.5;
  double _currentBrightness = 0.7;
  double _currentFontScale = 1.0;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    try {
      // Inicializar volumen
      VolumeController().listener((volume) {
        if (mounted) {
          setState(() {
            _currentVolume = volume;
          });
        }
      });
      
      final volume = await VolumeController().getVolume();
      setState(() {
        _currentVolume = volume;
      });

      // Inicializar brillo
      final brightness = await ScreenBrightness().current;
      setState(() {
        _currentBrightness = brightness;
      });
    } catch (e) {
      // Manejar errores silenciosamente
      debugPrint('Error inicializando configuraciones: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    final isLargeTablet = screenWidth > 900;

    // Ajustar altura y padding según el tamaño de pantalla
    final height = isLargeTablet
        ? AppDimensions.quickSettingsHeight + 10
        : isTablet
            ? AppDimensions.quickSettingsHeight + 5
            : AppDimensions.quickSettingsHeight;

    final horizontalPadding = isLargeTablet
        ? AppDimensions.paddingXL
        : isTablet
            ? AppDimensions.paddingL
            : AppDimensions.paddingM;

    return Container(
      height: height,
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: AppDimensions.paddingS,
      ),
      decoration: BoxDecoration(
        gradient: AppColors.surfaceGradient,
        border: Border(
          top: BorderSide(
            color: AppColors.accent.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Control de volumen
              Flexible(
                child: _buildQuickSetting(
                  icon: Icons.volume_up,
                  label: AppStrings.volume,
                  value: _currentVolume,
                  onChanged: _changeVolume,
                  onDecrease: () => _changeVolume(_currentVolume - 0.1),
                  onIncrease: () => _changeVolume(_currentVolume + 0.1),
                  isTablet: isTablet,
                ),
              ),

              // Control de brillo
              Flexible(
                child: _buildQuickSetting(
                  icon: Icons.brightness_6,
                  label: AppStrings.brightness,
                  value: _currentBrightness,
                  onChanged: _changeBrightness,
                  onDecrease: () => _changeBrightness(_currentBrightness - 0.1),
                  onIncrease: () => _changeBrightness(_currentBrightness + 0.1),
                  isTablet: isTablet,
                ),
              ),

              // Control de tamaño de fuente
              Flexible(
                child: _buildQuickSetting(
                  icon: Icons.text_fields,
                  label: AppStrings.fontSize,
                  value: (_currentFontScale - 0.8) / 0.7, // Normalizar a 0-1
                  onChanged: (value) => _changeFontScale(0.8 + (value * 0.7)),
                  onDecrease: () => _changeFontScale(_currentFontScale - 0.1),
                  onIncrease: () => _changeFontScale(_currentFontScale + 0.1),
                  isTablet: isTablet,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildQuickSetting({
    required IconData icon,
    required String label,
    required double value,
    required ValueChanged<double> onChanged,
    required VoidCallback onDecrease,
    required VoidCallback onIncrease,
    bool isTablet = false,
  }) {
    // Ajustar tamaños según el dispositivo
    final width = isTablet ? 140.0 : 100.0;
    final height = isTablet ? 55.0 : 45.0;
    final fontSize = isTablet ? 11.0 : 9.0;

    return SizedBox(
      width: width,
      height: height,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Etiqueta compacta
          Text(
            label,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),

          // Controles en una sola fila
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Botón disminuir
                _buildControlButton(
                  icon: Icons.remove,
                  onPressed: onDecrease,
                ),

                // Slider compacto
                Flexible(
                  child: Slider(
                    value: value.clamp(0.0, 1.0),
                    onChanged: onChanged,
                    activeColor: AppColors.primary,
                    inactiveColor: AppColors.borderLight,
                    thumbColor: AppColors.primary,
                  ),
                ),

                // Botón aumentar
                _buildControlButton(
                  icon: Icons.add,
                  onPressed: onIncrease,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: 24,
      height: 24,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonSecondary,
          foregroundColor: AppColors.primary,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          elevation: 1,
        ),
        child: Icon(
          icon,
          size: 12,
        ),
      ),
    );
  }

  Future<void> _changeVolume(double volume) async {
    try {
      final clampedVolume = volume.clamp(0.0, 1.0);
      VolumeController().setVolume(clampedVolume);
      setState(() {
        _currentVolume = clampedVolume;
      });
    } catch (e) {
      debugPrint('Error cambiando volumen: $e');
    }
  }

  Future<void> _changeBrightness(double brightness) async {
    try {
      final clampedBrightness = brightness.clamp(0.1, 1.0);
      await ScreenBrightness().setScreenBrightness(clampedBrightness);
      setState(() {
        _currentBrightness = clampedBrightness;
      });
    } catch (e) {
      debugPrint('Error cambiando brillo: $e');
    }
  }

  void _changeFontScale(double scale) {
    final clampedScale = scale.clamp(0.8, 1.5);
    setState(() {
      _currentFontScale = clampedScale;
    });
    
    // TODO: Implementar cambio global de escala de fuente
    // Esto requeriría reiniciar la app o usar un provider global
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Tamaño de letra: ${(clampedScale * 100).round()}%',
          style: const TextStyle(fontSize: AppDimensions.fontM),
        ),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
