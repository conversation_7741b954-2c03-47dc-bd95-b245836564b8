import 'dart:io';
import 'package:flutter/services.dart';
import 'environment_config.dart';

/// Configuración SSL para conexiones seguras
class SSLConfig {
  // Certificados SSL para pinning
  static const String _productionCertificate = '''
-----BEGIN CERTIFICATE-----
// AQUÍ VA TU CERTIFICADO SSL DE PRODUCCIÓN
// Reemplazar con el certificado real de tu dominio
-----END CERTIFICATE-----
''';

  static const String _testCertificate = '''
-----BEGIN CERTIFICATE-----
// AQUÍ VA TU CERTIFICADO SSL DE TESTING
// Reemplazar con el certificado real de tu servidor de pruebas
-----END CERTIFICATE-----
''';

  /// Obtener certificado según el entorno
  static String get certificate {
    if (EnvironmentConfig.isProduction) {
      return _productionCertificate;
    } else if (EnvironmentConfig.isTesting) {
      return _testCertificate;
    }
    return ''; // En desarrollo no usar SSL pinning
  }

  /// Verificar si SSL pinning está habilitado
  static bool get isSSLPinningEnabled => EnvironmentConfig.enableSSLPinning;

  /// Crear HttpClient con configuración SSL
  static HttpClient createSecureHttpClient() {
    final client = HttpClient();

    if (isSSLPinningEnabled && certificate.isNotEmpty) {
      // Configurar SSL pinning
      client.badCertificateCallback = (cert, host, port) {
        // Verificar certificado contra el certificado pinned
        return _verifyCertificate(cert, host);
      };
    } else if (EnvironmentConfig.isDevelopment) {
      // En desarrollo, permitir certificados auto-firmados
      client.badCertificateCallback = (cert, host, port) => true;
    }

    // Configurar timeouts
    client.connectionTimeout = EnvironmentConfig.connectionTimeout;

    return client;
  }

  /// Verificar certificado contra el certificado pinned
  static bool _verifyCertificate(X509Certificate cert, String host) {
    try {
      // Aquí implementarías la verificación real del certificado
      // Por ahora, solo verificamos que el host coincida
      
      // En producción, deberías verificar:
      // 1. La cadena de certificados
      // 2. La fecha de expiración
      // 3. El hash del certificado público
      // 4. El nombre del host
      
      print('🔒 Verificando certificado SSL para $host');
      
      // Verificación básica del host
      final subjectName = cert.subject.toLowerCase();
      final hostLower = host.toLowerCase();
      
      return subjectName.contains(hostLower) || 
             subjectName.contains('*.${hostLower.split('.').skip(1).join('.')}');
             
    } catch (e) {
      print('❌ Error verificando certificado SSL: $e');
      return false;
    }
  }

  /// Configuración de headers de seguridad
  static Map<String, String> get securityHeaders => {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Content-Security-Policy': "default-src 'self'",
  };

  /// Validar URL para HTTPS
  static bool isSecureUrl(String url) {
    if (!EnvironmentConfig.requireHttps) {
      return true; // En desarrollo, permitir HTTP
    }

    final uri = Uri.tryParse(url);
    return uri?.scheme == 'https';
  }

  /// Convertir URL HTTP a HTTPS si es necesario
  static String ensureHttps(String url) {
    if (!EnvironmentConfig.requireHttps) {
      return url; // En desarrollo, no forzar HTTPS
    }

    final uri = Uri.tryParse(url);
    if (uri == null) return url;

    if (uri.scheme == 'http') {
      return url.replaceFirst('http://', 'https://');
    }

    return url;
  }

  /// Información de configuración SSL
  static Map<String, dynamic> get sslInfo => {
    'sslPinningEnabled': isSSLPinningEnabled,
    'requireHttps': EnvironmentConfig.requireHttps,
    'environment': EnvironmentConfig.isDevelopment ? 'development' : 
                   EnvironmentConfig.isTesting ? 'testing' : 'production',
    'hasCertificate': certificate.isNotEmpty,
    'connectionTimeout': EnvironmentConfig.connectionTimeout.inSeconds,
  };

  /// Imprimir configuración SSL (solo en desarrollo)
  static void printSSLConfig() {
    if (!EnvironmentConfig.enableDebugLogs) return;

    print('🔒 Configuración SSL:');
    sslInfo.forEach((key, value) {
      print('   $key: $value');
    });
  }
}
