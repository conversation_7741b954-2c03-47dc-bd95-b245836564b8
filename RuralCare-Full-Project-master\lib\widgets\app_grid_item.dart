import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../models/app_item.dart';
import '../services/app_management_service.dart';

/// Widget para mostrar una aplicación en la grilla
class AppGridItem extends StatefulWidget {
  final AppItem app;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onFavoriteChanged;

  const AppGridItem({
    super.key,
    required this.app,
    required this.onTap,
    this.onLongPress,
    this.onFavoriteChanged,
  });

  @override
  State<AppGridItem> createState() => _AppGridItemState();
}

class _AppGridItemState extends State<AppGridItem> {
  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _isFavorite = AppManagementService.instance.isFavorite(widget.app.id);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Stack(
          children: [
            Container(
              padding: const EdgeInsets.all(16), // Más padding interno
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.18), // Más opacidad
                borderRadius: BorderRadius.circular(20), // Bordes más redondeados
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 2, // Borde más grueso
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 8, // Más sombra
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icono de la aplicación
                  _buildAppIcon(),

                  const SizedBox(height: 12), // Más espacio

                  // Nombre de la aplicación
                  _buildAppName(),
                ],
              ),
            ),
            // Botón de favoritos en la esquina superior derecha
            Positioned(
              top: 4,
              right: 4,
              child: GestureDetector(
                onTap: _toggleFavorite,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _isFavorite ? Icons.star : Icons.star_border,
                    color: _isFavorite ? Colors.amber : Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavorite() async {
    try {
      if (_isFavorite) {
        await AppManagementService.instance.removeFromFavorites(widget.app.id);
      } else {
        await AppManagementService.instance.addToFavorites(widget.app.id);
      }

      setState(() {
        _isFavorite = !_isFavorite;
      });

      // Notificar al padre si hay callback
      widget.onFavoriteChanged?.call();
    } catch (e) {
      // Manejar error silenciosamente
    }
  }

  Widget _buildAppIcon() {
    return Container(
      width: 250, // Aumentado de 48 a 64
      height: 180, // Aumentado de 48 a 64
      decoration: BoxDecoration(
        color: widget.app.color.withValues(alpha: 0.9), // Mucho menos transparente
        borderRadius: BorderRadius.circular(20), // Más redondeado
        border: Border.all(
          color: widget.app.color.withValues(alpha: 1.0), // Borde completamente opaco
          width: 3, // Borde más grueso
        ),
        boxShadow: [
          BoxShadow(
            color: widget.app.color.withValues(alpha: 0.6), // Sombra más visible
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: widget.app.appIcon != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Image.memory(
                widget.app.appIcon!,
                width: 64,
                height: 64,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildFallbackIcon();
                },
              ),
            )
          : _buildFallbackIcon(),
    );
  }

  Widget _buildFallbackIcon() {
    return Icon(
      widget.app.icon,
      size: 120, // Aumentado para que se vea mejor
      color: Colors.white, // Blanco para máximo contraste
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.5),
          offset: const Offset(2, 2),
          blurRadius: 4,
        ),
      ],
    );
  }

  Widget _buildAppName() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6),
      child: Text(
        widget.app.name,
        style: const TextStyle(
          fontSize: 24, // Aumentado de 12 a 14
          fontWeight: FontWeight.w800, // Más bold
          color: Colors.white,
          shadows: [
            Shadow(
              color: Colors.black,
              offset: Offset(0, 2),
              blurRadius: 4,
            ),
            Shadow(
              color: Colors.black54,
              offset: Offset(1, 1),
              blurRadius: 2,
            ),
          ],
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
