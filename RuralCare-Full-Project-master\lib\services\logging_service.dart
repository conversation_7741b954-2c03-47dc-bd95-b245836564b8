import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../config/environment_config.dart';

/// Niveles de log
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Servicio de logging mejorado
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();
  factory LoggingService() => _instance;
  LoggingService._internal();

  File? _logFile;
  bool _initialized = false;

  /// Inicializar el servicio de logging
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      if (EnvironmentConfig.isProduction) {
        // En producción, crear archivo de log
        final directory = await getApplicationDocumentsDirectory();
        final logDir = Directory('${directory.path}/logs');
        if (!await logDir.exists()) {
          await logDir.create(recursive: true);
        }

        final now = DateTime.now();
        final fileName = 'senior_launcher_${now.year}${now.month.toString().padLeft(2, '0')}.log';
        _logFile = File('${logDir.path}/$fileName');
      }

      _initialized = true;
      info('LoggingService', 'Sistema de logging inicializado');
    } catch (e) {
      print('❌ Error inicializando logging: $e');
    }
  }

  /// Log de debug
  void debug(String tag, String message) {
    _log(LogLevel.debug, tag, message);
  }

  /// Log de información
  void info(String tag, String message) {
    _log(LogLevel.info, tag, message);
  }

  /// Log de advertencia
  void warning(String tag, String message) {
    _log(LogLevel.warning, tag, message);
  }

  /// Log de error
  void error(String tag, String message, [dynamic error]) {
    final fullMessage = error != null ? '$message: $error' : message;
    _log(LogLevel.error, tag, fullMessage);
  }

  /// Log crítico
  void critical(String tag, String message, [dynamic error]) {
    final fullMessage = error != null ? '$message: $error' : message;
    _log(LogLevel.critical, tag, fullMessage);
  }

  /// Método interno de logging
  void _log(LogLevel level, String tag, String message) {
    if (!_shouldLog(level)) return;

    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(8);
    final tagStr = tag.padRight(20);
    final logMessage = '[$timestamp] $levelStr [$tagStr] $message';

    // Siempre imprimir en consola en desarrollo
    if (EnvironmentConfig.enableDebugLogs) {
      final emoji = _getEmojiForLevel(level);
      print('$emoji $logMessage');
    }

    // Escribir a archivo en producción
    if (EnvironmentConfig.isProduction && _logFile != null) {
      _writeToFile(logMessage);
    }
  }

  /// Verificar si se debe loggear este nivel
  bool _shouldLog(LogLevel level) {
    if (EnvironmentConfig.isDevelopment) {
      return true; // En desarrollo, loggear todo
    }

    if (EnvironmentConfig.isProduction) {
      // En producción, solo info, warning, error y critical
      return level.index >= LogLevel.info.index;
    }

    return true;
  }

  /// Obtener emoji para el nivel de log
  String _getEmojiForLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🔍';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.critical:
        return '🚨';
    }
  }

  /// Escribir a archivo de log
  void _writeToFile(String message) {
    try {
      _logFile?.writeAsStringSync('$message\n', mode: FileMode.append);
    } catch (e) {
      print('❌ Error escribiendo log: $e');
    }
  }

  /// Obtener logs recientes (para debugging)
  Future<List<String>> getRecentLogs({int maxLines = 100}) async {
    try {
      if (_logFile == null || !await _logFile!.exists()) {
        return [];
      }

      final content = await _logFile!.readAsString();
      final lines = content.split('\n');
      
      // Retornar las últimas líneas
      final startIndex = lines.length > maxLines ? lines.length - maxLines : 0;
      return lines.sublist(startIndex).where((line) => line.isNotEmpty).toList();
    } catch (e) {
      error('LoggingService', 'Error obteniendo logs recientes', e);
      return [];
    }
  }

  /// Limpiar logs antiguos
  Future<void> cleanOldLogs({int keepDays = 30}) async {
    try {
      if (!EnvironmentConfig.isProduction) return;

      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');
      
      if (!await logDir.exists()) return;

      final cutoffDate = DateTime.now().subtract(Duration(days: keepDays));
      
      await for (final entity in logDir.list()) {
        if (entity is File && entity.path.endsWith('.log')) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await entity.delete();
            info('LoggingService', 'Log antiguo eliminado: ${entity.path}');
          }
        }
      }
    } catch (e) {
      error('LoggingService', 'Error limpiando logs antiguos', e);
    }
  }

  /// Obtener estadísticas de logs
  Future<Map<String, dynamic>> getLogStats() async {
    try {
      if (_logFile == null || !await _logFile!.exists()) {
        return {'exists': false};
      }

      final stat = await _logFile!.stat();
      final content = await _logFile!.readAsString();
      final lines = content.split('\n').where((line) => line.isNotEmpty).length;

      return {
        'exists': true,
        'path': _logFile!.path,
        'size': stat.size,
        'modified': stat.modified.toIso8601String(),
        'lines': lines,
      };
    } catch (e) {
      error('LoggingService', 'Error obteniendo estadísticas de logs', e);
      return {'exists': false, 'error': e.toString()};
    }
  }
}
