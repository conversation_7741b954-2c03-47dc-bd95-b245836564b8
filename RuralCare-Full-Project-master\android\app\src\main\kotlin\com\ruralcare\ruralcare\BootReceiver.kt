package com.ruralcare.ruralcare

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

/**
 * Receiver que se ejecuta al iniciar el dispositivo
 * Reactiva automáticamente el modo kiosko y lanza RuralCare
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "RuralCareBoot"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d(TAG, "Boot completed o package replaced detectado")
                
                // Verificar si el modo kiosko estaba activo antes del reinicio
                val prefs = context.getSharedPreferences("ruralcare_prefs", Context.MODE_PRIVATE)
                val wasKioskActive = prefs.getBoolean("kiosk_mode_active", false)
                val wasDefaultLauncher = prefs.getBoolean("is_default_launcher", false)
                
                Log.d(TAG, "Estado previo - Kiosko: $wasKioskActive, Launcher: $wasDefaultLauncher")
                
                if (wasKioskActive || wasDefaultLauncher) {
                    // Iniciar el servicio de auto-kiosko
                    val serviceIntent = Intent(context, AutoKioskService::class.java)
                    serviceIntent.putExtra("restore_kiosk", wasKioskActive)
                    serviceIntent.putExtra("restore_launcher", wasDefaultLauncher)
                    
                    try {
                        context.startForegroundService(serviceIntent)
                        Log.d(TAG, "Servicio de auto-kiosko iniciado")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error iniciando servicio de auto-kiosko", e)
                        // Fallback: lanzar directamente la MainActivity
                        launchMainActivity(context)
                    }
                } else {
                    Log.d(TAG, "No hay configuración previa para restaurar")
                }
            }
        }
    }
    
    private fun launchMainActivity(context: Context) {
        try {
            val launchIntent = Intent(context, MainActivity::class.java)
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            launchIntent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            context.startActivity(launchIntent)
            Log.d(TAG, "MainActivity lanzada directamente")
        } catch (e: Exception) {
            Log.e(TAG, "Error lanzando MainActivity", e)
        }
    }
}
