import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../models/app_item.dart';
import '../services/app_manager_service.dart';
import '../services/app_launcher_service.dart';
import '../services/system_settings_service.dart';
import '../services/kiosk_mode_service.dart';
import '../services/app_management_service.dart';
import '../services/heartbeat_service.dart';
import '../services/backend_service.dart';
import '../services/wallpaper_service.dart';
import '../services/lock_screen_config_service.dart';
import '../screens/admin_screen.dart';
import '../screens/app_management_screen.dart';
import '../screens/category_apps_screen.dart';
import '../constants/app_categories.dart';
import '../models/app_category.dart';
import '../widgets/sidebar_navigation.dart';
import '../widgets/app_grid_item.dart';

/// Área principal de contenido con diseño tipo dashboard
class MainContentArea extends StatefulWidget {
  const MainContentArea({super.key});

  @override
  State<MainContentArea> createState() => _MainContentAreaState();
}

enum SidebarMode {
  none,
  apps,
  edit,
  settings,
  admin, // Nuevo modo admin
}

class _MainContentAreaState extends State<MainContentArea> {
  NavigationCategory _selectedCategory = NavigationCategory.favorites;
  List<AppItem> _apps = [];
  bool _isLoading = true;
  SidebarMode _sidebarMode = SidebarMode.none;

  // Variables de estado para configuraciones
  double _currentVolume = 0.7;
  double _currentBrightness = 0.8;
  double _currentFontScale = 1.0;
  bool _wifiEnabled = true;
  bool _bluetoothEnabled = false;
  bool _autoRotateEnabled = true;

  // Variables para el modo edición
  int _gridCrossAxisCount = 4; // Número de columnas en la grilla
  bool _editMode = false;
  String _editModeType = ''; // 'remove', 'move', ''

  @override
  void initState() {
    super.initState();
    _loadApps();
    _loadSystemSettings();
  }

  Future<void> _loadSystemSettings() async {
    try {
      await SystemSettingsService.instance.initialize();

      final volume = await SystemSettingsService.instance.getVolume();
      final brightness = await SystemSettingsService.instance.getBrightness();
      final fontScale = SystemSettingsService.instance.getFontScale();
      final wifiEnabled = SystemSettingsService.instance.getWifiEnabled();
      final bluetoothEnabled = SystemSettingsService.instance.getBluetoothEnabled();
      final autoRotateEnabled = SystemSettingsService.instance.getAutoRotateEnabled();

      setState(() {
        _currentVolume = volume;
        _currentBrightness = brightness;
        _currentFontScale = fontScale;
        _wifiEnabled = wifiEnabled;
        _bluetoothEnabled = bluetoothEnabled;
        _autoRotateEnabled = autoRotateEnabled;
      });
    } catch (e) {
      print('Error cargando configuraciones del sistema: $e');
    }
  }

  Future<void> _loadApps() async {
    setState(() => _isLoading = true);
    
    try {
      List<AppItem> apps;
      
      // Filtrar apps según la categoría seleccionada
      if (_selectedCategory.id == 'favorites') {
        apps = AppManagementService.instance.getFavoriteApps();
      } else if (_selectedCategory.id == 'all') {
        apps = AppManagementService.instance.getVisibleApps();
      } else {
        // Filtrar por categoría específica
        final category = AppCategories.getCategoryById(_selectedCategory.id);
        apps = AppManagementService.instance.getAppsByCategory(category);
      }
      
      setState(() {
        _apps = apps;
        _isLoading = false;
      });
    } catch (e) {
      print('Error cargando apps: $e');
      setState(() => _isLoading = false);
    }
  }

  void _onCategorySelected(NavigationCategory category) {
    // Navegar a la pantalla de categorías en lugar de cambiar el estado local
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CategoryAppsScreen(
          initialCategory: category,
        ),
      ),
    );
  }

  void _navigateToCategoriesScreen() {
    // Navegar directamente a la pantalla de categorías con la categoría por defecto
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CategoryAppsScreen(
          initialCategory: null, // Usará la categoría por defecto (favoritos)
        ),
      ),
    );
  }

  void _toggleSidebarMode(SidebarMode mode) {
    setState(() {
      _sidebarMode = _sidebarMode == mode ? SidebarMode.none : mode;
    });
  }

  void _handleFavorites() {
    // Cambiar a la categoría de favoritos
    _onCategorySelected(NavigationCategory.favorites);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Mostrando aplicaciones favoritas'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Ayuda',
          style: TextStyle(
            fontSize: AppDimensions.fontL,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Guía de uso del launcher:',
                style: TextStyle(fontSize: AppDimensions.fontM),
              ),
              const SizedBox(height: AppDimensions.paddingL),
              _buildHelpItem(
                Icons.touch_app,
                'Toque los botones grandes para abrir aplicaciones',
              ),
              _buildHelpItem(
                Icons.menu,
                'Use el menú para ver todas las aplicaciones',
              ),
              _buildHelpItem(
                Icons.edit,
                'Use el lápiz para organizar aplicaciones',
              ),
              _buildHelpItem(
                Icons.settings,
                'Use configuración para ajustar volumen y brillo',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Entendido',
              style: TextStyle(fontSize: AppDimensions.fontM),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: AppDimensions.iconM,
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: AppDimensions.fontS),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF1976D2), // Azul más intenso arriba
              const Color(0xFF42A5F5), // Azul más claro abajo
            ],
          ),
        ),
        child: Row(
          children: [
            // Barra lateral fija
            _buildFixedSidebar(),

            // Contenido principal
            Expanded(
              child: Stack(
                children: [
                  // Área principal de aplicaciones
                  _buildMainContent(),

                  // Panel deslizable según el modo
                  if (_sidebarMode != SidebarMode.none)
                    _buildSlidingPanel(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFixedSidebar() {
    return Container(
      width: 100, // Aumentado de 80 a 100
      decoration: BoxDecoration(
        color: const Color(0xFF0D47A1), // Azul más oscuro para la barra lateral
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          const Spacer(flex: 1), // Espacio superior flexible

          // Botón de ayuda (?)
          _buildSidebarButton(
            icon: Icons.help_outline,
            onTap: _showHelpDialog,
          ),

          const Spacer(flex: 2), // Espacio flexible entre botones

          // Botón de favoritos (estrella)
          _buildSidebarButton(
            icon: Icons.star,
            onTap: _handleFavorites,
          ),

          const Spacer(flex: 2), // Espacio flexible entre botones

          // Botón de edición (lápiz)
          _buildSidebarButton(
            icon: Icons.edit,
            onTap: () => _toggleSidebarMode(SidebarMode.edit),
            isActive: _sidebarMode == SidebarMode.edit,
          ),

          const Spacer(flex: 2), // Espacio flexible entre botones

          // Botón de configuración
          _buildSidebarButton(
            icon: Icons.settings,
            onTap: () => _toggleSidebarMode(SidebarMode.settings),
            onLongPress: _showAdminAccess, // Toque largo para modo admin
            isActive: _sidebarMode == SidebarMode.settings,
          ),

          const Spacer(flex: 2), // Espacio flexible entre botones

          // Botón de menú hamburguesa
          _buildSidebarButton(
            icon: Icons.menu,
            onTap: () => _navigateToCategoriesScreen(),
            isActive: false, // Ya no necesitamos el estado activo
          ),

          const Spacer(flex: 1), // Espacio inferior flexible
        ],
      ),
    );
  }

  Widget _buildSidebarButton({
    required IconData icon,
    required VoidCallback onTap,
    VoidCallback? onLongPress,
    bool isActive = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      child: Material(
        color: isActive ? Colors.white.withValues(alpha: 0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        elevation: isActive ? 4 : 0,
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            width: 100, // Aumentado de 56 a 80
            height: 100, // Aumentado de 56 a 80
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: isActive ? Border.all(color: Colors.white.withValues(alpha: 0.4), width: 3) : null,
              boxShadow: isActive ? [
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 80, // Aumentado de 28 a 36
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      padding: const EdgeInsets.all(24), // Más padding
      child: Column(
        children: [
          // Header simplificado
          _buildSimpleHeader(),

          const SizedBox(height: 20), // Más espacio

          // Contenido de aplicaciones
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleHeader() {
    return Row(
      children: [
        // Icono de la categoría
        Container(
          width: 64, // Aumentado de 48 a 64
          height: 64, // Aumentado de 48 a 64
          decoration: BoxDecoration(
            color: _selectedCategory.color,
            borderRadius: BorderRadius.circular(16), // Más redondeado
            boxShadow: [
              BoxShadow(
                color: _selectedCategory.color.withValues(alpha: 0.4),
                blurRadius: 12, // Más sombra
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            _selectedCategory.icon,
            color: Colors.white,
            size: 36, // Aumentado de 28 a 36
          ),
        ),

        const SizedBox(width: 20), // Más espacio

        // Título de la categoría
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _selectedCategory.label,
                style: const TextStyle(
                  fontSize: 28, // Aumentado de 24 a 28
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                '${_apps.length} aplicaciones',
                style: TextStyle(
                  fontSize: 16, // Aumentado de 14 a 16
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSlidingPanel() {
    return GestureDetector(
      onTap: () => setState(() => _sidebarMode = SidebarMode.none),
      child: Container(
        color: const Color.fromARGB(255, 255, 255, 255).withValues(alpha: 0.5),
        child: Row(
          children: [
            // Panel deslizable
            Container(
              width: 400, // Aumentado de 320 a 400
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.25),
                    blurRadius: 20, // Más sombra
                    offset: const Offset(6, 0),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(2, 0),
                  ),
                ],
              ),
              child: _buildPanelContent(),
            ),

            // Área transparente para cerrar
            Expanded(child: Container()),
          ],
        ),
      ),
    );
  }

  Widget _buildPanelContent() {
    switch (_sidebarMode) {
      case SidebarMode.apps:
        return _buildAppsPanel();
      case SidebarMode.edit:
        return _buildEditPanel();
      case SidebarMode.settings:
        return _buildSettingsPanel();
      case SidebarMode.admin:
        return _buildAdminPanel();
      case SidebarMode.none:
        return Container();
    }
  }

  Widget _buildAppsPanel() {
    return Container(
      color: const Color(0xFF1976D2),
      child: Column(
        children: [
          // Header del panel
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              children: [
                const Icon(
                  Icons.category,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: AppDimensions.paddingM),
                const Expanded(
                  child: Text(
                    'Categorías:',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 255, 255, 255),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Lista de categorías
          Expanded(
            child: Container(
              color: const Color.fromARGB(255, 0, 0, 0),
              child: ListView.builder(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                itemCount: _getAvailableCategories().length,
                itemBuilder: (context, index) {
                  final category = _getAvailableCategories()[index];
                  return _buildCategoryListItem(category);
                },
              ),
            ),
          ),

          // Botón volver
          Container(
            color: const Color(0xFF1976D2),
            padding: const EdgeInsets.all(24), // Más padding
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => setState(() => _sidebarMode = SidebarMode.none),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF1976D2),
                  padding: const EdgeInsets.symmetric(vertical: 20), // Más alto
                  elevation: 4,
                  shadowColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16), // Más redondeado
                  ),
                ),
                child: const Text(
                  'VOLVER',
                  style: TextStyle(
                    fontSize: 20, // Más grande
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryListItem(NavigationCategory category) {
    final isSelected = category == _selectedCategory;

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Material(
        color: isSelected ? category.color.withValues(alpha: 0.1) : Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        elevation: 2,
        child: InkWell(
          onTap: () {
            setState(() => _sidebarMode = SidebarMode.none);
            _onCategorySelected(category);
          },
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(20), // Aumentado padding
            child: Row(
              children: [
                // Icono de la categoría
                Container(
                  width: 64, // Aumentado de 48 a 64
                  height: 64, // Aumentado de 48 a 64
                  decoration: BoxDecoration(
                    color: category.color,
                    borderRadius: BorderRadius.circular(16), // Más redondeado
                    boxShadow: [
                      BoxShadow(
                        color: category.color.withValues(alpha: 0.4),
                        blurRadius: 12, // Más sombra
                        offset: const Offset(0, 4),
                      ),
                      BoxShadow(
                        color: category.color.withValues(alpha: 0.2),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 36, // Aumentado de 28 a 36
                  ),
                ),

                const SizedBox(width: 20), // Más espacio

                // Nombre de la categoría
                Expanded(
                  child: Text(
                    category.label,
                    style: TextStyle(
                      fontSize: 22, // Aumentado de 18 a 22
                      fontWeight: FontWeight.w700, // Más bold
                      color: isSelected ? category.color : Colors.black, // Cambiar a negro para que se vea
                    ),
                  ),
                ),

                // Indicador de selección
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: category.color,
                    size: 32, // Aumentado de 24 a 32
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppListItem(AppItem app) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingS),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        elevation: 2,
        child: InkWell(
          onTap: () => _handleAppTap(app),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              children: [
                // Icono de la app
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: app.appIcon != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                          child: Image.memory(
                            app.appIcon!,
                            width: 48,
                            height: 48,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          app.icon,
                          color: AppColors.primary,
                          size: 32,
                        ),
                ),

                const SizedBox(width: AppDimensions.paddingM),

                // Nombre de la app
                Expanded(
                  child: Text(
                    app.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),

                // Icono de favorito
                Icon(
                  app.isFavorite ? Icons.star : Icons.star_border,
                  color: app.isFavorite ? Colors.amber : Colors.grey,
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEditPanel() {
    return Container(
      color: const Color(0xFF1976D2),
      child: Column(
        children: [
          // Header del panel
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              children: [
                const Icon(
                  Icons.edit,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: AppDimensions.paddingM),
                const Expanded(
                  child: Text(
                    'Editar aplicaciones',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Botones de edición
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              children: [
                // Indicador de modo activo
                if (_editMode)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: _editModeType == 'remove'
                          ? Colors.red.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _editModeType == 'remove'
                            ? Colors.red
                            : Colors.orange,
                        width: 2,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _editModeType == 'remove'
                              ? Icons.remove_circle
                              : Icons.swap_horiz,
                          color: _editModeType == 'remove'
                              ? Colors.red
                              : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _editModeType == 'remove'
                                ? 'Modo QUITAR activo - Toca una app para ocultarla'
                                : 'Modo MOVER activo - Toca una app para cambiar su categoría',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _editModeType == 'remove'
                                  ? Colors.red
                                  : Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                _buildEditButton(
                  icon: Icons.remove_circle,
                  label: _editMode && _editModeType == 'remove' ? 'DESACTIVAR QUITAR' : 'QUITAR APP',
                  color: Colors.red,
                  onTap: _handleRemoveApp,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                _buildEditButton(
                  icon: Icons.swap_horiz,
                  label: _editMode && _editModeType == 'move' ? 'DESACTIVAR MOVER' : 'MOVER APP',
                  color: Colors.orange,
                  onTap: _handleMoveApp,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                _buildEditButton(
                  icon: Icons.zoom_in,
                  label: 'ACERCAR',
                  color: Colors.green,
                  onTap: _handleZoomIn,
                ),
                const SizedBox(height: AppDimensions.paddingM),
                _buildEditButton(
                  icon: Icons.zoom_out,
                  label: 'ALEJAR',
                  color: Colors.blue,
                  onTap: _handleZoomOut,
                ),
              ],
            ),
          ),

          const Spacer(),

          // Botón volver
          Container(
            color: const Color(0xFF1976D2),
            padding: const EdgeInsets.all(24), // Más padding
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => setState(() => _sidebarMode = SidebarMode.none),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF1976D2),
                  padding: const EdgeInsets.symmetric(vertical: 20), // Más alto
                  elevation: 4,
                  shadowColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16), // Más redondeado
                  ),
                ),
                child: const Text(
                  'VOLVER',
                  style: TextStyle(
                    fontSize: 20, // Más grande
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 20), // Aumentado de 16 a 20
          elevation: 4,
          shadowColor: color.withValues(alpha: 0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16), // Más redondeado
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 28), // Aumentado de 24 a 28
            const SizedBox(width: 12), // Más espacio
            Text(
              label,
              style: const TextStyle(
                fontSize: 18, // Aumentado de 16 a 18
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleRemoveApp() {
    if (_editMode && _editModeType == 'remove') {
      // Desactivar modo quitar
      setState(() {
        _editMode = false;
        _editModeType = '';
      });
      _showEditMessage('Modo quitar aplicación desactivado');
    } else {
      // Activar modo quitar
      setState(() {
        _editMode = true;
        _editModeType = 'remove';
      });
      _showEditMessage('Modo quitar aplicación activado - Toque una app para ocultarla');
    }
  }

  void _handleMoveApp() {
    if (_editMode && _editModeType == 'move') {
      // Desactivar modo mover
      setState(() {
        _editMode = false;
        _editModeType = '';
      });
      _showEditMessage('Modo mover aplicación desactivado');
    } else {
      // Activar modo mover
      setState(() {
        _editMode = true;
        _editModeType = 'move';
      });
      _showEditMessage('Modo mover aplicación activado - Mantenga presionada una app para moverla');
    }
  }

  void _handleZoomIn() {
    if (_gridCrossAxisCount > 2) {
      setState(() => _gridCrossAxisCount--);
      _showEditMessage('Vista acercada - ${_gridCrossAxisCount} columnas');
    } else {
      _showEditMessage('Vista ya está al máximo acercamiento');
    }
  }

  void _handleZoomOut() {
    if (_gridCrossAxisCount < 6) {
      setState(() => _gridCrossAxisCount++);
      _showEditMessage('Vista alejada - ${_gridCrossAxisCount} columnas');
    } else {
      _showEditMessage('Vista ya está al máximo alejamiento');
    }
  }

  void _showEditMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
    );
  }

  Widget _buildSettingsPanel() {
    return Container(
      color: const Color(0xFF1976D2),
      child: Column(
        children: [
          // Header del panel
          Container(
            padding: const EdgeInsets.all(24), // Más padding
            child: Row(
              children: [
                const Icon(
                  Icons.settings,
                  color: Color.fromARGB(255, 0, 0, 0),
                  size: 36, // Más grande
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Text(
                    'Opciones',
                    style: TextStyle(
                      fontSize: 26, // Más grande
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 0, 0, 0),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Configuraciones
          Expanded(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(24), // Más padding
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Volumen
                    _buildSettingSlider(
                      icon: Icons.volume_up,
                      label: 'Volumen',
                      value: _currentVolume,
                      onChanged: (value) async {
                        setState(() => _currentVolume = value);
                        await SystemSettingsService.instance.setVolume(value);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingL),

                    // Brillo
                    _buildSettingSlider(
                      icon: Icons.brightness_6,
                      label: 'Brillo',
                      value: _currentBrightness,
                      onChanged: (value) async {
                        setState(() => _currentBrightness = value);
                        await SystemSettingsService.instance.setBrightness(value);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingL),

                    // Tamaño de letra
                    _buildSettingSlider(
                      icon: Icons.text_fields,
                      label: 'Tamaño de la letra',
                      value: (_currentFontScale - 0.5) / 1.5, // Convertir de 0.5-2.0 a 0.0-1.0
                      onChanged: (value) async {
                        final fontScale = 0.5 + (value * 1.5); // Convertir de 0.0-1.0 a 0.5-2.0
                        setState(() => _currentFontScale = fontScale);
                        await SystemSettingsService.instance.setFontScale(fontScale);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingXL),

                    // Checkboxes
                    _buildSettingCheckbox(
                      icon: Icons.wifi,
                      label: 'Activar Wi-Fi',
                      value: _wifiEnabled,
                      onChanged: (value) async {
                        setState(() => _wifiEnabled = value ?? false);
                        await SystemSettingsService.instance.setWifiEnabled(value ?? false);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingM),

                    _buildSettingCheckbox(
                      icon: Icons.bluetooth,
                      label: 'Activar Bluetooth',
                      value: _bluetoothEnabled,
                      onChanged: (value) async {
                        setState(() => _bluetoothEnabled = value ?? false);
                        await SystemSettingsService.instance.setBluetoothEnabled(value ?? false);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingM),

                    _buildSettingCheckbox(
                      icon: Icons.screen_rotation,
                      label: 'Giro automático de pantalla',
                      value: _autoRotateEnabled,
                      onChanged: (value) async {
                        setState(() => _autoRotateEnabled = value ?? true);
                        await SystemSettingsService.instance.setAutoRotateEnabled(value ?? true);
                      },
                    ),

                    const SizedBox(height: AppDimensions.paddingXL),

                    // Sección de configuración de internet
                    Material(
                      color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      child: InkWell(
                        onTap: () async {
                          await SystemSettingsService.instance.openWifiSettings();
                        },
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(AppDimensions.paddingM),
                          child: const Text(
                            'CONFIGURACIÓN DE INTERNET',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1976D2),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Botón volver
          Container(
            color: const Color(0xFF1976D2),
            padding: const EdgeInsets.all(24), // Más padding
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => setState(() => _sidebarMode = SidebarMode.none),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF1976D2),
                  padding: const EdgeInsets.symmetric(vertical: 20), // Más alto
                  elevation: 4,
                  shadowColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16), // Más redondeado
                  ),
                ),
                child: const Text(
                  'VOLVER',
                  style: TextStyle(
                    fontSize: 20, // Más grande
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingSlider({
    required IconData icon,
    required String label,
    required double value,
    required ValueChanged<double> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: const Color(0xFF1976D2),
              size: 32, // Aumentado de 24 a 32
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 20, // Aumentado de 16 a 20
                fontWeight: FontWeight.w600, // Más bold
                color: Colors.black, // Cambiar a negro para que se vea
              ),
            ),
          ],
        ),
        const SizedBox(height: 16), // Más espacio
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF1976D2),
            inactiveTrackColor: const Color(0xFF1976D2).withValues(alpha: 0.3),
            thumbColor: const Color(0xFF1976D2),
            overlayColor: const Color(0xFF1976D2).withValues(alpha: 0.2),
            trackHeight: 8, // Más grueso
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 16), // Más grande
          ),
          child: Slider(
            value: value,
            onChanged: onChanged,
            min: 0.0,
            max: 1.0,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingCheckbox({
    required IconData icon,
    required String label,
    required bool value,
    required ValueChanged<bool?> onChanged,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: const Color(0xFF1976D2),
          size: 32, // Aumentado de 24 a 32
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 20, // Aumentado de 16 a 20
              fontWeight: FontWeight.w600, // Más bold
              color: Colors.black, // Cambiar a negro para que se vea
            ),
          ),
        ),
        Transform.scale(
          scale: 1.3, // Hacer el checkbox más grande
          child: Checkbox(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF1976D2),
            checkColor: Colors.white,
            materialTapTargetSize: MaterialTapTargetSize.padded,
          ),
        ),
      ],
    );
  }



  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (_apps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedCategory.icon,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'No hay aplicaciones en esta categoría',
              style: TextStyle(
                fontSize: 18,
                color: const Color.fromARGB(255, 0, 0, 0).withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _gridCrossAxisCount,
          childAspectRatio: 1.0,
          crossAxisSpacing: AppDimensions.paddingS,
          mainAxisSpacing: AppDimensions.paddingS,
        ),
        itemCount: _apps.length,
        itemBuilder: (context, index) {
          return Stack(
            children: [
              AppGridItem(
                app: _apps[index],
                onTap: () => _handleAppTap(_apps[index]),
              ),

              // Indicador de modo edición
              if (_editMode)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: _editModeType == 'remove'
                          ? Colors.red.withValues(alpha: 0.9)
                          : Colors.orange.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(color: Colors.white, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      _editModeType == 'remove'
                          ? Icons.remove_circle_outline
                          : Icons.swap_horiz,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _handleAppTap(AppItem app) async {
    // Si estamos en modo edición, manejar según el tipo
    if (_editMode) {
      if (_editModeType == 'remove') {
        await _handleRemoveAppAction(app);
        return;
      } else if (_editModeType == 'move') {
        await _handleMoveAppAction(app);
        return;
      }
    }

    // Comportamiento normal: abrir la aplicación
    // Mostrar indicador de carga
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Abriendo ${app.name}...'),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        duration: const Duration(seconds: 1),
      ),
    );

    // Intentar lanzar la aplicación
    final success = await AppLauncherService.instance.launchApp(app.packageName);

    if (!success) {
      // Mostrar error si no se pudo abrir
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No se pudo abrir ${app.name}'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
        );
      }
    }
  }

  /// Manejar acción de quitar/ocultar aplicación
  Future<void> _handleRemoveAppAction(AppItem app) async {
    // Mostrar diálogo de confirmación
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Ocultar ${app.name}'),
        content: const Text('¿Estás seguro de que quieres ocultar esta aplicación? Podrás mostrarla nuevamente desde el panel de administrador.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Ocultar', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Ocultar la aplicación
        await AppManagementService.instance.hideApp(app.id);

        // Recargar apps
        await _loadApps();

        // Mostrar mensaje de confirmación
        _showEditMessage('${app.name} ocultada correctamente');

        // Desactivar modo edición
        setState(() {
          _editMode = false;
          _editModeType = '';
        });
      } catch (e) {
        _showEditMessage('Error ocultando ${app.name}: $e');
      }
    }
  }

  /// Manejar acción de mover aplicación
  Future<void> _handleMoveAppAction(AppItem app) async {
    // Mostrar diálogo para seleccionar nueva categoría
    final newCategory = await showDialog<AppCategory>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Mover ${app.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Selecciona la nueva categoría:'),
            const SizedBox(height: 16),
            ...AppCategories.allCategories.map((category) =>
              ListTile(
                leading: Icon(category.icon, color: category.color),
                title: Text(category.name),
                selected: app.category.id == category.id,
                onTap: () => Navigator.of(context).pop(category),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );

    if (newCategory != null && newCategory.id != app.category.id) {
      try {
        // Recategorizar la aplicación
        await AppManagementService.instance.recategorizeApp(app.id, newCategory);

        // Recargar apps
        await _loadApps();

        // Mostrar mensaje de confirmación
        _showEditMessage('${app.name} movida a ${newCategory.name}');

        // Desactivar modo edición
        setState(() {
          _editMode = false;
          _editModeType = '';
        });
      } catch (e) {
        _showEditMessage('Error moviendo ${app.name}: $e');
      }
    }
  }

  /// Mostrar acceso al modo administrador
  void _showAdminAccess() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.admin_panel_settings, color: Colors.red, size: 28),
            SizedBox(width: 12),
            Text('Modo Administrador'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Ingrese el PIN de administrador:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            TextField(
              obscureText: true,
              keyboardType: TextInputType.number,
              maxLength: 4,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 24, letterSpacing: 8),
              decoration: const InputDecoration(
                hintText: '••••',
                border: OutlineInputBorder(),
                counterText: '',
              ),
              onSubmitted: (pin) {
                if (pin == '1234') {
                  Navigator.of(context).pop();
                  _enterAdminMode();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('PIN incorrecto'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Entrar al modo administrador
  void _enterAdminMode() {
    setState(() {
      _sidebarMode = SidebarMode.admin;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔓 Modo Administrador Activado'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Panel de administrador
  Widget _buildAdminPanel() {
    return Column(
      children: [
        // Header del panel admin
        Container(
          color: const Color(0xFF7C3AED), // Color violeta para admin
          padding: const EdgeInsets.all(24),
          child: const Row(
            children: [
              Icon(
                Icons.admin_panel_settings,
                color: Colors.white,
                size: 36,
              ),
              SizedBox(width: 16),
              Expanded(
                child: Text(
                  'Panel Administrador',
                  style: TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Contenido del panel admin
        Expanded(
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.all(24),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sección: Gestión de Aplicaciones
                  _buildAdminSection(
                    title: 'Gestión de Aplicaciones',
                    icon: Icons.apps,
                    children: [
                      _buildAdminButton(
                        icon: Icons.apps,
                        label: 'Gestionar Aplicaciones',
                        color: Colors.blue,
                        onTap: _openAppManagement,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.add_circle,
                        label: 'Mostrar App Oculta',
                        color: Colors.green,
                        onTap: _showHiddenAppsDialog,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.remove_circle,
                        label: 'Ocultar Aplicación',
                        color: Colors.red,
                        onTap: _hideAppDialog,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.refresh,
                        label: 'Actualizar Lista',
                        color: Colors.blue,
                        onTap: () => _loadApps(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Sección: Configuración del Sistema
                  _buildAdminSection(
                    title: 'Sistema',
                    icon: Icons.settings_system_daydream,
                    children: [
                      _buildAdminButton(
                        icon: Icons.lock,
                        label: 'Configurar Kiosk Mode',
                        color: Colors.orange,
                        onTap: _openKioskConfiguration,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.lock_open,
                        label: 'Verificar Estado Kiosk',
                        color: Colors.blue,
                        onTap: _checkKioskStatus,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.power_settings_new,
                        label: 'Activar Kiosk Rápido',
                        color: Colors.green,
                        onTap: _quickEnableKiosk,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.power_off,
                        label: 'Desactivar Kiosk',
                        color: Colors.red,
                        onTap: _quickDisableKiosk,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.palette,
                        label: 'Personalizar Tema',
                        color: Colors.purple,
                        onTap: () => _showMessage('Función: Personalizar Tema'),
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.lock_outline,
                        label: 'Configurar Pantalla de Bloqueo',
                        color: Colors.indigo,
                        onTap: _configureLockScreen,
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.backup,
                        label: 'Backup Configuración',
                        color: Colors.teal,
                        onTap: () => _showMessage('Función: Backup'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Sección: Estadísticas
                  _buildAdminSection(
                    title: 'Estadísticas',
                    icon: Icons.analytics,
                    children: [
                      // Estado de conectividad con backend
                      _buildConnectivityStatus(),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.bar_chart,
                        label: 'Ver Uso de Apps',
                        color: Colors.indigo,
                        onTap: () => _showMessage('Función: Estadísticas'),
                      ),
                      const SizedBox(height: 12),
                      _buildAdminButton(
                        icon: Icons.history,
                        label: 'Historial de Actividad',
                        color: Colors.brown,
                        onTap: () => _showMessage('Función: Historial'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // Botón salir del modo admin
        Container(
          color: const Color(0xFF7C3AED),
          padding: const EdgeInsets.all(24),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => setState(() => _sidebarMode = SidebarMode.none),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF7C3AED),
                padding: const EdgeInsets.symmetric(vertical: 20),
                elevation: 4,
                shadowColor: Colors.black26,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                'SALIR DEL MODO ADMIN',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Construir sección del panel admin
  Widget _buildAdminSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: const Color(0xFF7C3AED), size: 24),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  /// Construir botón del panel admin
  Widget _buildAdminButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          elevation: 3,
          shadowColor: color.withValues(alpha: 0.4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 12),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Mostrar mensaje informativo
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        backgroundColor: const Color(0xFF7C3AED),
      ),
    );
  }

  /// Abrir configuración completa de Kiosk Mode
  void _openKioskConfiguration() async {
    try {
      // Navegar a la pantalla de administrador completa
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const AdminScreen(),
        ),
      );

      // Actualizar estado al regresar
      setState(() {
        _sidebarMode = SidebarMode.none;
      });
    } catch (e) {
      _showMessage('Error abriendo configuración: $e');
    }
  }

  /// Verificar estado del Kiosk Mode
  void _checkKioskStatus() async {
    try {
      final isActive = await KioskModeService.instance.isKioskModeActive();
      final isDeviceAdmin = await KioskModeService.instance.isDeviceAdminEnabled();

      String status = '';
      if (isActive) {
        status = '🔒 Kiosk Mode: ACTIVO';
      } else if (isDeviceAdmin) {
        status = '🔓 Kiosk Mode: INACTIVO (Admin habilitado)';
      } else {
        status = '❌ Kiosk Mode: Sin permisos de administrador';
      }

      _showMessage(status);
    } catch (e) {
      _showMessage('Error verificando Kiosk Mode: $e');
    }
  }

  /// Activar Kiosk Mode rápido
  void _quickEnableKiosk() async {
    try {
      final success = await KioskModeService.instance.setupKioskMode();
      if (success) {
        _showMessage('✅ Kiosk Mode activado correctamente');
      } else {
        _showMessage('❌ Error activando Kiosk Mode. Usa configuración completa.');
      }
    } catch (e) {
      _showMessage('Error: $e');
    }
  }

  /// Desactivar Kiosk Mode rápido
  void _quickDisableKiosk() async {
    try {
      final success = await KioskModeService.instance.exitKioskMode();
      if (success) {
        _showMessage('✅ Kiosk Mode desactivado');
      } else {
        _showMessage('❌ Error desactivando Kiosk Mode');
      }
    } catch (e) {
      _showMessage('Error: $e');
    }
  }

  /// Obtener categorías disponibles con contadores
  List<NavigationCategory> _getAvailableCategories() {
    final stats = AppManagementService.instance.getAppStatistics();

    return [
      // Favoritos
      NavigationCategory(
        id: 'favorites',
        label: 'Favoritos',
        icon: Icons.star,
        color: const Color(0xFFFFD700),
        appCount: stats['Favoritos'] ?? 0,
      ),

      // Categorías dinámicas desde AppCategories (excluyendo favoritos para evitar duplicación)
      ...AppCategories.allCategories
          .where((category) => category.id != 'favorites') // Excluir favoritos
          .map((category) => NavigationCategory(
            id: category.id,
            label: category.name,
            icon: category.icon,
            color: category.color,
            appCount: stats[category.name] ?? 0,
          ))
          .where((cat) => cat.appCount > 0), // Solo mostrar categorías con apps

      // Todas las apps
      NavigationCategory(
        id: 'all',
        label: 'Todas',
        icon: Icons.apps,
        color: const Color(0xFF795548),
        appCount: stats['Total'] ?? 0,
      ),
    ];
  }

  /// Construir widget de estado de conectividad
  Widget _buildConnectivityStatus() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getConnectivityInfo(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 12),
                Text('Verificando conectividad...'),
              ],
            ),
          );
        }

        final data = snapshot.data!;
        final isConnected = data['isConnected'] ?? false;
        final isRegistered = data['isRegistered'] ?? false;
        final deviceId = data['deviceId'] ?? 'Desconocido';

        Color statusColor;
        IconData statusIcon;
        String statusText;

        if (isConnected && isRegistered) {
          statusColor = Colors.green;
          statusIcon = Icons.cloud_done;
          statusText = 'Conectado al servidor';
        } else if (isRegistered) {
          statusColor = Colors.orange;
          statusIcon = Icons.cloud_off;
          statusText = 'Registrado, sin conexión';
        } else {
          statusColor = Colors.red;
          statusIcon = Icons.cloud_off;
          statusText = 'Sin conexión';
        }

        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: statusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: statusColor),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(statusIcon, color: statusColor, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      statusText,
                      style: TextStyle(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                'ID: $deviceId',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontFamily: 'monospace',
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Obtener información de conectividad
  Future<Map<String, dynamic>> _getConnectivityInfo() async {
    try {
      final heartbeatService = HeartbeatService();
      return heartbeatService.getConnectionStats();
    } catch (e) {
      return {
        'isConnected': false,
        'isRegistered': false,
        'deviceId': 'Error',
      };
    }
  }

  /// Abrir gestión completa de aplicaciones
  void _openAppManagement() async {
    try {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const AppManagementScreen(),
        ),
      );

      // Actualizar apps al regresar
      await _loadApps();
      setState(() {
        _sidebarMode = SidebarMode.none;
      });
    } catch (e) {
      _showMessage('Error abriendo gestión de apps: $e');
    }
  }

  /// Mostrar diálogo para mostrar apps ocultas
  void _showHiddenAppsDialog() async {
    try {
      // Obtener todas las apps instaladas
      final allApps = await AppManagementService.instance.getAllInstalledApps();
      final hiddenApps = allApps.where((app) => AppManagementService.instance.isHidden(app.id)).toList();

      if (hiddenApps.isEmpty) {
        _showMessage('No hay aplicaciones ocultas');
        return;
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
          title: const Text('Aplicaciones Ocultas'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: hiddenApps.length,
              itemBuilder: (context, index) {
                final app = hiddenApps[index];
                return ListTile(
                  leading: app.appIcon != null
                      ? Image.memory(app.appIcon!, width: 32, height: 32)
                      : Icon(app.icon),
                  title: Text(app.name),
                  subtitle: Text(app.packageName),
                  trailing: ElevatedButton(
                    onPressed: () async {
                      await AppManagementService.instance.showApp(app.id);
                      Navigator.of(context).pop();
                      _showMessage('${app.name} ahora es visible');
                      await _loadApps(); // Recargar apps
                    },
                    child: const Text('Mostrar'),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cerrar'),
            ),
          ],
        ),
      );
      }
    } catch (e) {
      _showMessage('Error: $e');
    }
  }

  /// Mostrar diálogo para ocultar aplicaciones
  void _hideAppDialog() {
    final visibleApps = AppManagementService.instance.getVisibleApps();

    if (visibleApps.isEmpty) {
      _showMessage('No hay aplicaciones para ocultar');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ocultar Aplicación'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: visibleApps.length,
            itemBuilder: (context, index) {
              final app = visibleApps[index];
              return ListTile(
                leading: app.appIcon != null
                    ? Image.memory(app.appIcon!, width: 32, height: 32)
                    : Icon(app.icon),
                title: Text(app.name),
                subtitle: Text(app.packageName),
                trailing: ElevatedButton(
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  onPressed: () async {
                    await AppManagementService.instance.hideApp(app.id);
                    if (mounted) {
                      Navigator.of(context).pop();
                      _showMessage('${app.name} ocultada');
                      await _loadApps(); // Recargar apps
                    }
                  },
                  child: const Text('Ocultar', style: TextStyle(color: Colors.white)),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Configurar pantalla de bloqueo
  void _configureLockScreen() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock_outline, color: Colors.indigo, size: 28),
            SizedBox(width: 12),
            Text('Configurar Pantalla de Bloqueo'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Selecciona cómo quieres configurar la pantalla de bloqueo:',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 20),

              // Opción 1: Configuración nativa de Android
              ListTile(
                leading: const Icon(Icons.android, color: Colors.green),
                title: const Text('Configuración Nativa de Android'),
                subtitle: const Text('Abrir configuración del sistema'),
                onTap: () {
                  Navigator.of(context).pop();
                  _openNativeLockScreenSettings();
                },
              ),

              const Divider(),

              // Opción 2: Configuración personalizada
              ListTile(
                leading: const Icon(Icons.palette, color: Colors.purple),
                title: const Text('Configuración Personalizada'),
                subtitle: const Text('Personalizar fondo y logo'),
                onTap: () {
                  Navigator.of(context).pop();
                  _openCustomLockScreenConfig();
                },
              ),

              const Divider(),

              // Opción 3: Fondos predefinidos
              ListTile(
                leading: const Icon(Icons.wallpaper, color: Colors.blue),
                title: const Text('Fondos Predefinidos'),
                subtitle: const Text('Seleccionar de galería'),
                onTap: () {
                  Navigator.of(context).pop();
                  _showPredefinedWallpapers();
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Abrir configuración nativa de Android
  void _openNativeLockScreenSettings() async {
    try {
      // Usar el canal nativo para abrir configuración de pantalla de bloqueo
      await MethodChannel('ruralcare/settings')
          .invokeMethod('openLockScreenSettings');
      _showMessage('Abriendo configuración de pantalla de bloqueo...');
    } catch (e) {
      _showMessage('Error: No se pudo abrir la configuración nativa');
    }
  }

  /// Abrir configuración personalizada
  void _openCustomLockScreenConfig() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Configuración Personalizada'),
        content: SizedBox(
          width: 500,
          height: 400,
          child: Column(
            children: [
              // Selector de fondo
              Card(
                child: ListTile(
                  leading: const Icon(Icons.image),
                  title: const Text('Fondo de Pantalla'),
                  subtitle: const Text('Cambiar imagen de fondo'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectCustomWallpaper,
                ),
              ),

              const SizedBox(height: 12),

              // Configuración de logo
              Card(
                child: ListTile(
                  leading: const Icon(Icons.business),
                  title: const Text('Logo/Marca'),
                  subtitle: const Text('Agregar logo institucional'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _configureLogo,
                ),
              ),

              const SizedBox(height: 12),

              // Texto personalizado
              Card(
                child: ListTile(
                  leading: const Icon(Icons.text_fields),
                  title: const Text('Texto Personalizado'),
                  subtitle: const Text('Mensaje en pantalla de bloqueo'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _configureCustomText,
                ),
              ),

              const SizedBox(height: 12),

              // Vista previa
              Card(
                child: ListTile(
                  leading: const Icon(Icons.preview),
                  title: const Text('Vista Previa'),
                  subtitle: const Text('Ver cómo se verá'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _showLockScreenPreview,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  /// Mostrar fondos predefinidos
  void _showPredefinedWallpapers() {
    final wallpapers = [
      {'name': 'Azul Corporativo', 'type': 'blue', 'color': Colors.blue},
      {'name': 'Naturaleza', 'type': 'nature', 'color': Colors.green},
      {'name': 'Abstracto', 'type': 'abstract', 'color': Colors.purple},
      {'name': 'Minimalista', 'type': 'minimal', 'color': Colors.grey},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fondos Predefinidos'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: wallpapers.length,
            itemBuilder: (context, index) {
              final wallpaper = wallpapers[index];
              return InkWell(
                onTap: () async {
                  Navigator.of(context).pop();
                  _showMessage('Aplicando fondo ${wallpaper['name']}...');

                  // Aplicar wallpaper usando el servicio existente
                  final wallpaperService = WallpaperService();
                  final success = await wallpaperService.setTestWallpaper(wallpaper['type'] as String);

                  if (success) {
                    _showMessage('✅ Fondo aplicado exitosamente');
                  } else {
                    _showMessage('❌ Error aplicando el fondo');
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: wallpaper['color'] as Color,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.wallpaper,
                        size: 40,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        wallpaper['name'] as String,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  /// Seleccionar wallpaper personalizado
  void _selectCustomWallpaper() async {
    try {
      _showMessage('Seleccionando imagen de fondo...');

      final String? imagePath = await LockScreenConfigService.instance.selectBackgroundImage();

      if (imagePath != null) {
        _showMessage('✅ Imagen de fondo seleccionada exitosamente');

        // Preguntar si quiere aplicar inmediatamente
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Imagen Seleccionada'),
              content: const Text('¿Deseas aplicar esta imagen como fondo de pantalla ahora?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Más Tarde'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    Navigator.of(context).pop();
                    await LockScreenConfigService.instance.setCustomConfigEnabled(true);
                    final success = await LockScreenConfigService.instance.applyConfiguration();
                    _showMessage(success ? '✅ Configuración aplicada' : '❌ Error aplicando configuración');
                  },
                  child: const Text('Aplicar Ahora'),
                ),
              ],
            ),
          );
        }
      } else {
        _showMessage('Selección cancelada');
      }
    } catch (e) {
      _showMessage('❌ Error seleccionando imagen: $e');
    }
  }

  /// Configurar logo
  void _configureLogo() async {
    try {
      _showMessage('Seleccionando logo...');

      final String? logoPath = await LockScreenConfigService.instance.selectLogoImage();

      if (logoPath != null) {
        // Mostrar opciones de posición del logo
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
            title: const Text('Configurar Logo'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Selecciona la posición del logo:'),
                const SizedBox(height: 16),

                // Opciones de posición
                ListTile(
                  leading: const Icon(Icons.vertical_align_top),
                  title: const Text('Parte Superior'),
                  onTap: () async {
                    await LockScreenConfigService.instance.setLogoPosition('top-center');
                    if (mounted) {
                      Navigator.of(context).pop();
                      _showMessage('✅ Logo configurado en la parte superior');
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.vertical_align_center),
                  title: const Text('Centro'),
                  onTap: () async {
                    await LockScreenConfigService.instance.setLogoPosition('center');
                    if (mounted) {
                      Navigator.of(context).pop();
                      _showMessage('✅ Logo configurado en el centro');
                    }
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.vertical_align_bottom),
                  title: const Text('Parte Inferior'),
                  onTap: () async {
                    await LockScreenConfigService.instance.setLogoPosition('bottom-center');
                    if (mounted) {
                      Navigator.of(context).pop();
                      _showMessage('✅ Logo configurado en la parte inferior');
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancelar'),
              ),
            ],
          ),
        );
        }
      } else {
        _showMessage('Selección cancelada');
      }
    } catch (e) {
      _showMessage('❌ Error configurando logo: $e');
    }
  }

  /// Configurar texto personalizado
  void _configureCustomText() {
    final TextEditingController textController = TextEditingController();
    Color selectedColor = Colors.white;
    String selectedPosition = 'bottom-center';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Configurar Texto Personalizado'),
          content: SizedBox(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Campo de texto
                TextField(
                  controller: textController,
                  decoration: const InputDecoration(
                    labelText: 'Texto a mostrar',
                    hintText: 'Ej: Bienvenido a la Tablet Institucional',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),

                const SizedBox(height: 16),

                // Selector de color
                Row(
                  children: [
                    const Text('Color: '),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        // Mostrar selector de colores básicos
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Seleccionar Color'),
                            content: Wrap(
                              children: [
                                Colors.white,
                                Colors.black,
                                Colors.blue,
                                Colors.red,
                                Colors.green,
                                Colors.orange,
                                Colors.purple,
                                Colors.grey,
                              ].map((color) => GestureDetector(
                                onTap: () {
                                  setState(() => selectedColor = color);
                                  Navigator.of(context).pop();
                                },
                                child: Container(
                                  width: 40,
                                  height: 40,
                                  margin: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: color,
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              )).toList(),
                            ),
                          ),
                        );
                      },
                      child: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: selectedColor,
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Selector de posición
                DropdownButtonFormField<String>(
                  value: selectedPosition,
                  decoration: const InputDecoration(
                    labelText: 'Posición',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'top-center', child: Text('Parte Superior')),
                    DropdownMenuItem(value: 'center', child: Text('Centro')),
                    DropdownMenuItem(value: 'bottom-center', child: Text('Parte Inferior')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => selectedPosition = value);
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (textController.text.isNotEmpty) {
                  await LockScreenConfigService.instance.setCustomText(textController.text);
                  await LockScreenConfigService.instance.setTextColor(selectedColor);
                  await LockScreenConfigService.instance.setTextPosition(selectedPosition);

                  if (mounted) {
                    Navigator.of(context).pop();
                    _showMessage('✅ Texto personalizado configurado');
                  }
                } else {
                  _showMessage('⚠️ Por favor ingresa un texto');
                }
              },
              child: const Text('Guardar'),
            ),
          ],
        ),
      ),
    );
  }

  /// Mostrar vista previa
  void _showLockScreenPreview() async {
    try {
      final config = await LockScreenConfigService.instance.getCurrentConfig();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => Dialog(
          child: Container(
            width: 300,
            height: 500,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Fondo
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.blue.shade900,
                          Colors.blue.shade600,
                        ],
                      ),
                    ),
                    child: config['backgroundImage'] != null
                        ? FutureBuilder<Uint8List?>(
                            future: LockScreenConfigService.instance.getImageBytes(config['backgroundImage']),
                            builder: (context, snapshot) {
                              if (snapshot.hasData && snapshot.data != null) {
                                return Image.memory(
                                  snapshot.data!,
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                );
                              }
                              return const SizedBox();
                            },
                          )
                        : null,
                  ),

                  // Overlay oscuro
                  Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.black.withValues(alpha: 0.3),
                  ),

                  // Logo
                  if (config['logoImage'] != null)
                    Positioned(
                      top: config['logoPosition'] == 'top-center' ? 60 : null,
                      bottom: config['logoPosition'] == 'bottom-center' ? 60 : null,
                      left: 0,
                      right: 0,
                      child: config['logoPosition'] == 'center'
                          ? Center(
                              child: FutureBuilder<Uint8List?>(
                                future: LockScreenConfigService.instance.getImageBytes(config['logoImage']),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData && snapshot.data != null) {
                                    return Image.memory(
                                      snapshot.data!,
                                      width: 80,
                                      height: 80,
                                      fit: BoxFit.contain,
                                    );
                                  }
                                  return const Icon(Icons.business, size: 80, color: Colors.white);
                                },
                              ),
                            )
                          : Center(
                              child: FutureBuilder<Uint8List?>(
                                future: LockScreenConfigService.instance.getImageBytes(config['logoImage']),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData && snapshot.data != null) {
                                    return Image.memory(
                                      snapshot.data!,
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.contain,
                                    );
                                  }
                                  return const Icon(Icons.business, size: 60, color: Colors.white);
                                },
                              ),
                            ),
                    ),

                  // Texto personalizado
                  if (config['customText'].isNotEmpty)
                    Positioned(
                      top: config['textPosition'] == 'top-center' ? 120 : null,
                      bottom: config['textPosition'] == 'bottom-center' ? 40 : null,
                      left: 20,
                      right: 20,
                      child: config['textPosition'] == 'center'
                          ? Center(
                              child: Text(
                                config['customText'],
                                style: TextStyle(
                                  color: config['textColor'],
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            )
                          : Text(
                              config['customText'],
                              style: TextStyle(
                                color: config['textColor'],
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),

                  // Simulación de elementos de pantalla de bloqueo
                  Positioned(
                    top: 20,
                    right: 20,
                    child: Text(
                      '12:34',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  Positioned(
                    bottom: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
      }
    } catch (e) {
      _showMessage('❌ Error mostrando vista previa: $e');
    }
  }
}
