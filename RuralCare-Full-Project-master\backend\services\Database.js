const mysql = require('mysql2/promise');

class Database {
  constructor() {
    this.pool = null;
  }

  // 🔌 Conectar a MySQL
  async connect() {
    try {
      this.pool = mysql.createPool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'senior_launcher',
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
        acquireTimeout: 60000,
        timeout: 60000,
        charset: 'utf8mb4',
        timezone: '+00:00'
      });

      // Probar conexión
      const connection = await this.pool.getConnection();
      await connection.execute('SELECT 1');
      connection.release();
      
      return true;
    } catch (error) {
      console.error('❌ Error conectando a MySQL:', error.message);
      throw error;
    }
  }

  // 📊 Ejecutar query
  async query(sql, params = []) {
    try {
      const [rows, fields] = await this.pool.execute(sql, params);
      return { rows, fields };
    } catch (error) {
      console.error('❌ Error ejecutando query:', error.message);
      console.error('SQL:', sql);
      console.error('Params:', params);
      throw error;
    }
  }

  // 📝 Insertar registro
  async insert(table, data) {
    const columns = Object.keys(data).join(', ');
    const placeholders = Object.keys(data).map(() => '?').join(', ');
    const values = Object.values(data);
    
    const sql = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`;
    const result = await this.query(sql, values);
    
    return result.rows.insertId;
  }

  // 🔄 Actualizar registro
  async update(table, data, where, whereParams = []) {
    const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(data), ...whereParams];
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${where}`;
    const result = await this.query(sql, values);
    
    return result.rows.affectedRows;
  }

  // 🗑️ Eliminar registro
  async delete(table, where, whereParams = []) {
    const sql = `DELETE FROM ${table} WHERE ${where}`;
    const result = await this.query(sql, whereParams);
    
    return result.rows.affectedRows;
  }

  // 🔍 Buscar un registro
  async findOne(table, where, whereParams = []) {
    const sql = `SELECT * FROM ${table} WHERE ${where} LIMIT 1`;
    const result = await this.query(sql, whereParams);
    
    return result.rows[0] || null;
  }

  // 🔍 Buscar múltiples registros
  async findMany(table, where = '1=1', whereParams = [], orderBy = '', limit = '') {
    let sql = `SELECT * FROM ${table} WHERE ${where}`;
    
    if (orderBy) sql += ` ORDER BY ${orderBy}`;
    if (limit) sql += ` LIMIT ${limit}`;
    
    const result = await this.query(sql, whereParams);
    return result.rows;
  }

  // 📊 Contar registros
  async count(table, where = '1=1', whereParams = []) {
    const sql = `SELECT COUNT(*) as count FROM ${table} WHERE ${where}`;
    const result = await this.query(sql, whereParams);
    
    return result.rows[0].count;
  }

  // 🔐 Gestión de sesiones (reemplaza Redis)
  async saveSession(userId, tokenHash, expiresAt, ipAddress, userAgent) {
    return await this.insert('user_sessions', {
      user_id: userId,
      token_hash: tokenHash,
      expires_at: expiresAt,
      ip_address: ipAddress,
      user_agent: userAgent
    });
  }

  async getSession(tokenHash) {
    return await this.findOne('user_sessions', 'token_hash = ? AND expires_at > NOW()', [tokenHash]);
  }

  async deleteSession(tokenHash) {
    return await this.delete('user_sessions', 'token_hash = ?', [tokenHash]);
  }

  async cleanExpiredSessions() {
    return await this.delete('user_sessions', 'expires_at < NOW()');
  }

  // 🚦 Rate Limiting (reemplaza Redis)
  async checkRateLimit(ipAddress, endpoint, windowMs, maxRequests) {
    const windowStart = new Date(Date.now() - windowMs);
    
    // Limpiar registros antiguos
    await this.delete('rate_limits', 'window_start < ?', [windowStart]);
    
    // Buscar registro actual
    const existing = await this.findOne(
      'rate_limits', 
      'ip_address = ? AND endpoint = ?', 
      [ipAddress, endpoint]
    );
    
    if (existing) {
      if (existing.requests_count >= maxRequests) {
        return { allowed: false, remaining: 0 };
      }
      
      // Incrementar contador
      await this.update(
        'rate_limits',
        { requests_count: existing.requests_count + 1 },
        'id = ?',
        [existing.id]
      );
      
      return { 
        allowed: true, 
        remaining: maxRequests - existing.requests_count - 1 
      };
    } else {
      // Crear nuevo registro
      await this.insert('rate_limits', {
        ip_address: ipAddress,
        endpoint: endpoint,
        requests_count: 1,
        window_start: new Date()
      });
      
      return { allowed: true, remaining: maxRequests - 1 };
    }
  }

  // 📈 Estadísticas de dispositivos
  async updateDeviceStats(deviceId, statDate, stats) {
    const existing = await this.findOne(
      'device_statistics',
      'device_id = ? AND stat_date = ?',
      [deviceId, statDate]
    );
    
    if (existing) {
      return await this.update(
        'device_statistics',
        stats,
        'id = ?',
        [existing.id]
      );
    } else {
      return await this.insert('device_statistics', {
        device_id: deviceId,
        stat_date: statDate,
        ...stats
      });
    }
  }

  // 🧹 Limpiar datos antiguos
  async cleanup() {
    try {
      // Ejecutar procedimiento de limpieza
      await this.query('CALL cleanup_old_data()');
      console.log('✅ Limpieza de datos completada');
    } catch (error) {
      console.error('❌ Error en limpieza de datos:', error.message);
    }
  }

  // 🔌 Cerrar conexión
  async close() {
    if (this.pool) {
      await this.pool.end();
      console.log('✅ Conexión MySQL cerrada');
    }
  }

  // 🔍 Obtener información de la base de datos
  async getInfo() {
    try {
      const version = await this.query('SELECT VERSION() as version');
      const tables = await this.query('SHOW TABLES');

      return {
        version: version.rows[0].version,
        tables: tables.rows.length,
        connected: true
      };
    } catch (error) {
      return {
        connected: false,
        error: error.message
      };
    }
  }
}

// Exportar instancia singleton
module.exports = new Database();
