import 'package:flutter/material.dart';
import 'app_item.dart';
import '../constants/app_colors.dart';

/// Modelo que representa una categoría de aplicaciones
class AppCategory {
  final String id;
  final String name;
  final String displayName;
  final IconData icon;
  final Color color;
  final List<String> packageNames;
  final String description;

  const AppCategory({
    required this.id,
    required this.name,
    required this.displayName,
    required this.icon,
    required this.color,
    required this.packageNames,
    required this.description,
  });

  /// Categorías predefinidas para personas mayores
  static const List<AppCategory> defaultCategories = [
    AppCategory(
      id: 'favorites',
      name: 'Favori<PERSON>',
      displayName: 'Mis Favoritos',
      icon: Icons.star,
      color: AppColors.warning,
      packageNames: [
        'com.android.dialer',
        'com.android.mms',
        'com.android.camera2',
        'com.android.contacts',
      ],
      description: 'Aplicaciones más usadas',
    ),
    
    AppCategory(
      id: 'communication',
      name: 'Comunicación',
      displayName: 'Comunicación',
      icon: Icons.chat,
      color: AppColors.success,
      packageNames: [
        'com.android.dialer',
        'com.android.mms',
        'com.whatsapp',
        'com.facebook.orca',
        'com.skype.raider',
        'com.viber.voip',
        'org.telegram.messenger',
        'com.google.android.gm',
        'com.microsoft.office.outlook',
        'com.android.contacts',
      ],
      description: 'Llamadas, mensajes y email',
    ),
    
    AppCategory(
      id: 'entertainment',
      name: 'Entretenimiento',
      displayName: 'Entretenimiento',
      icon: Icons.play_circle_fill,
      color: AppColors.error,
      packageNames: [
        'com.google.android.youtube',
        'com.netflix.mediaclient',
        'com.spotify.music',
        'com.amazon.avod.thirdpartyclient',
        'com.disney.disneyplus',
        'com.google.android.apps.youtube.music',
        'com.android.music',
        'com.google.android.apps.photos',
        'com.android.gallery3d',
      ],
      description: 'Videos, música y fotos',
    ),
    
    AppCategory(
      id: 'social',
      name: 'Redes Sociales',
      displayName: 'Redes Sociales',
      icon: Icons.people,
      color: AppColors.primary,
      packageNames: [
        'com.facebook.katana',
        'com.instagram.android',
        'com.twitter.android',
        'com.linkedin.android',
        'com.whatsapp',
        'com.facebook.orca',
      ],
      description: 'Facebook, Instagram y más',
    ),
    
    AppCategory(
      id: 'tools',
      name: 'Herramientas',
      displayName: 'Herramientas',
      icon: Icons.build,
      color: AppColors.info,
      packageNames: [
        'com.android.camera2',
        'com.google.android.calculator',
        'com.android.calendar',
        'com.google.android.apps.maps',
        'com.waze',
        'com.android.chrome',
        'com.google.android.apps.photos',
        'com.dropbox.android',
      ],
      description: 'Cámara, calculadora y utilidades',
    ),
    
    AppCategory(
      id: 'health',
      name: 'Salud',
      displayName: 'Salud y Bienestar',
      icon: Icons.favorite,
      color: AppColors.accent,
      packageNames: [
        'com.fitbit.FitbitMobile',
        'com.samsung.android.app.shealth',
        'com.google.android.apps.fitness',
        'com.android.emergency',
        'com.sec.android.app.safetyassurance',
      ],
      description: 'Salud, ejercicio y emergencias',
    ),
    
    AppCategory(
      id: 'shopping',
      name: 'Compras',
      displayName: 'Compras',
      icon: Icons.shopping_cart,
      color: AppColors.warning,
      packageNames: [
        'com.amazon.mShop.android.shopping',
        'com.paypal.android.p2pmobile',
        'com.mercadolibre',
        'com.ebay.mobile',
      ],
      description: 'Tiendas online y pagos',
    ),
    
    AppCategory(
      id: 'settings',
      name: 'Configuración',
      displayName: 'Configuración',
      icon: Icons.settings,
      color: AppColors.textSecondary,
      packageNames: [
        'com.android.settings',
        'com.samsung.android.settings',
      ],
      description: 'Ajustes del dispositivo',
    ),
  ];

  /// Obtener categoría por ID
  static AppCategory? getCategoryById(String id) {
    try {
      return defaultCategories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Obtener categoría de una app por su package name
  static AppCategory? getCategoryForApp(String packageName) {
    for (final category in defaultCategories) {
      if (category.packageNames.contains(packageName)) {
        return category;
      }
    }
    return null;
  }

  /// Filtrar apps por categoría
  static List<AppItem> filterAppsByCategory(List<AppItem> apps, String categoryId) {
    final category = getCategoryById(categoryId);
    if (category == null) return apps;
    
    if (categoryId == 'favorites') {
      // Para favoritas, mostrar las apps más usadas o marcadas como favoritas
      return apps.where((app) => 
        category.packageNames.contains(app.packageName) || 
        app.isFavorite
      ).toList();
    }
    
    return apps.where((app) => 
      category.packageNames.contains(app.packageName)
    ).toList();
  }

  /// Obtener todas las categorías que tienen apps
  static List<AppCategory> getCategoriesWithApps(List<AppItem> apps) {
    final categoriesWithApps = <AppCategory>[];
    
    for (final category in defaultCategories) {
      final categoryApps = filterAppsByCategory(apps, category.id);
      if (categoryApps.isNotEmpty) {
        categoriesWithApps.add(category);
      }
    }
    
    return categoriesWithApps;
  }

  /// Convertir a Map para serialización
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'icon': icon.codePoint,
      'color': color.value,
      'packageNames': packageNames,
      'description': description,
    };
  }

  /// Crear desde Map
  factory AppCategory.fromMap(Map<String, dynamic> map) {
    return AppCategory(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      displayName: map['displayName'] ?? '',
      icon: IconData(map['icon'] ?? Icons.apps.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(map['color'] ?? Colors.blue.value),
      packageNames: List<String>.from(map['packageNames'] ?? []),
      description: map['description'] ?? '',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppCategory(id: $id, name: $name, apps: ${packageNames.length})';
  }
}
