import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../models/app_item.dart';
import '../models/app_category.dart';
import '../constants/app_categories.dart';
import '../services/app_management_service.dart';

/// Pantalla completa de gestión de aplicaciones
class AppManagementScreen extends StatefulWidget {
  const AppManagementScreen({super.key});

  @override
  State<AppManagementScreen> createState() => _AppManagementScreenState();
}

class _AppManagementScreenState extends State<AppManagementScreen> {
  final AppManagementService _appService = AppManagementService.instance;
  
  List<AppItem> _allApps = [];
  List<AppItem> _filteredApps = [];
  String _searchQuery = '';
  AppCategory? _selectedCategory;
  bool _showOnlyFavorites = false;
  bool _showHiddenApps = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadApps();
  }

  Future<void> _loadApps() async {
    setState(() => _isLoading = true);
    
    try {
      await _appService.refreshInstalledApps();
      _allApps = _appService.getVisibleApps();
      _applyFilters();
    } catch (e) {
      _showMessage('Error cargando aplicaciones: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _applyFilters() async {
    setState(() {
      _filteredApps = _allApps.where((app) {
        // Filtro de búsqueda
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!app.name.toLowerCase().contains(query) &&
              !app.packageName.toLowerCase().contains(query)) {
            return false;
          }
        }

        // Filtro de categoría
        if (_selectedCategory != null && app.category.id != _selectedCategory!.id) {
          return false;
        }

        // Filtro de favoritos
        if (_showOnlyFavorites && !_appService.isFavorite(app.id)) {
          return false;
        }

        return true;
      }).toList();

      // Agregar apps ocultas si se solicita
      if (_showHiddenApps) {
        // Por ahora, simplemente mostrar mensaje
        // TODO: Implementar mostrar apps ocultas
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestión de Aplicaciones'),
        backgroundColor: AppColors.adminPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadApps,
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra de búsqueda y filtros
          _buildSearchAndFilters(),
          
          // Estadísticas
          _buildStatistics(),
          
          // Lista de aplicaciones
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildAppsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Column(
        children: [
          // Barra de búsqueda
          TextField(
            decoration: const InputDecoration(
              hintText: 'Buscar aplicaciones...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              _searchQuery = value;
              _applyFilters();
            },
          ),
          
          const SizedBox(height: 12),
          
          // Filtros
          Wrap(
            spacing: 8,
            children: [
              // Filtro de categoría
              DropdownButton<AppCategory?>(
                value: _selectedCategory,
                hint: const Text('Todas las categorías'),
                items: [
                  const DropdownMenuItem<AppCategory?>(
                    value: null,
                    child: Text('Todas las categorías'),
                  ),
                  ...AppCategories.allCategories.map((category) =>
                    DropdownMenuItem<AppCategory?>(
                      value: category,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(category.icon, size: 16, color: category.color),
                          const SizedBox(width: 8),
                          Text(category.name),
                        ],
                      ),
                    ),
                  ),
                ],
                onChanged: (value) {
                  _selectedCategory = value;
                  _applyFilters();
                },
              ),
              
              // Filtro de favoritos
              FilterChip(
                label: const Text('Solo Favoritos'),
                selected: _showOnlyFavorites,
                onSelected: (selected) {
                  _showOnlyFavorites = selected;
                  _applyFilters();
                },
              ),
              
              // Filtro de apps ocultas
              FilterChip(
                label: const Text('Mostrar Ocultas'),
                selected: _showHiddenApps,
                onSelected: (selected) {
                  _showHiddenApps = selected;
                  _applyFilters();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatistics() {
    final stats = _appService.getAppStatistics();
    
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.adminPrimary.withValues(alpha: 0.1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Total', stats['Total'] ?? 0, Icons.apps),
          _buildStatItem('Favoritos', stats['Favoritos'] ?? 0, Icons.star),
          _buildStatItem('Ocultas', stats['Ocultas'] ?? 0, Icons.visibility_off),
          _buildStatItem('Mostrando', _filteredApps.length, Icons.list),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppColors.adminPrimary),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.adminPrimary,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildAppsList() {
    if (_filteredApps.isEmpty) {
      return const Center(
        child: Text(
          'No se encontraron aplicaciones',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredApps.length,
      itemBuilder: (context, index) {
        final app = _filteredApps[index];
        return _buildAppListItem(app);
      },
    );
  }

  Widget _buildAppListItem(AppItem app) {
    final isFavorite = _appService.isFavorite(app.id);
    final isHidden = _appService.isHidden(app.id);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: app.category.color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: app.appIcon != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(
                    app.appIcon!,
                    width: 48,
                    height: 48,
                    fit: BoxFit.cover,
                  ),
                )
              : Icon(
                  app.icon,
                  color: app.category.color,
                  size: 24,
                ),
        ),
        title: Text(
          app.name,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            decoration: isHidden ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(app.packageName, style: const TextStyle(fontSize: 12)),
            Row(
              children: [
                Icon(app.category.icon, size: 14, color: app.category.color),
                const SizedBox(width: 4),
                Text(app.category.name, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Botón favorito
            IconButton(
              icon: Icon(
                isFavorite ? Icons.star : Icons.star_border,
                color: isFavorite ? Colors.amber : Colors.grey,
              ),
              onPressed: () => _toggleFavorite(app),
            ),
            
            // Botón ocultar/mostrar
            IconButton(
              icon: Icon(
                isHidden ? Icons.visibility : Icons.visibility_off,
                color: isHidden ? Colors.green : Colors.grey,
              ),
              onPressed: () => _toggleVisibility(app),
            ),
            
            // Menú de opciones
            PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(value, app),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'recategorize',
                  child: Row(
                    children: [
                      Icon(Icons.category),
                      SizedBox(width: 8),
                      Text('Cambiar Categoría'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'info',
                  child: Row(
                    children: [
                      Icon(Icons.info),
                      SizedBox(width: 8),
                      Text('Información'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavorite(AppItem app) async {
    if (_appService.isFavorite(app.id)) {
      await _appService.removeFromFavorites(app.id);
      _showMessage('${app.name} quitada de favoritos');
    } else {
      await _appService.addToFavorites(app.id);
      _showMessage('${app.name} agregada a favoritos');
    }
    setState(() {});
  }

  void _toggleVisibility(AppItem app) async {
    if (_appService.isHidden(app.id)) {
      await _appService.showApp(app.id);
      _showMessage('${app.name} ahora es visible');
    } else {
      await _appService.hideApp(app.id);
      _showMessage('${app.name} ocultada');
    }
    await _loadApps(); // Recargar para actualizar listas
  }

  void _handleMenuAction(String action, AppItem app) {
    switch (action) {
      case 'recategorize':
        _showRecategorizeDialog(app);
        break;
      case 'info':
        _showAppInfoDialog(app);
        break;
    }
  }

  void _showRecategorizeDialog(AppItem app) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Cambiar categoría de ${app.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AppCategories.allCategories.map((category) =>
            ListTile(
              leading: Icon(category.icon, color: category.color),
              title: Text(category.name),
              selected: app.category.id == category.id,
              onTap: () async {
                await _appService.recategorizeApp(app.id, category);
                Navigator.of(context).pop();
                _showMessage('${app.name} movida a ${category.name}');
                _applyFilters();
              },
            ),
          ).toList(),
        ),
      ),
    );
  }

  void _showAppInfoDialog(AppItem app) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(app.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Package: ${app.packageName}'),
            Text('Categoría: ${app.category.name}'),
            Text('Favorita: ${_appService.isFavorite(app.id) ? 'Sí' : 'No'}'),
            Text('Oculta: ${_appService.isHidden(app.id) ? 'Sí' : 'No'}'),
            Text('Sistema: ${app.isSystemApp ? 'Sí' : 'No'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cerrar'),
          ),
        ],
      ),
    );
  }

  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.adminPrimary,
      ),
    );
  }
}
