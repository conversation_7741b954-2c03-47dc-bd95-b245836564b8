const express = require('express');
const Database = require('../services/Database');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();

// 📱 Registrar dispositivo
router.post('/register', async (req, res) => {
  try {
    const {
      device_id,
      device_name,
      launcher_version,
      android_version,
      model,
      manufacturer,
      brand,
      sdk_int,
      device_info
    } = req.body;
    
    if (!device_id) {
      return res.status(400).json({
        error: 'device_id es requerido'
      });
    }
    
    // Verificar si ya existe
    const existing = await Database.findOne('devices', 'device_id = ?', [device_id]);
    
    if (existing) {
      // Actualizar información
      await Database.update('devices', {
        device_name,
        launcher_version,
        android_version,
        model,
        manufacturer,
        brand,
        sdk_int,
        device_info: JSON.stringify(device_info || {}),
        last_seen: new Date(),
        ip_address: req.ip,
        status: 'active'
      }, 'device_id = ?', [device_id]);
      
      res.json({
        success: true,
        message: 'Dispositivo actualizado',
        device_id: existing.id
      });
    } else {
      // Crear nuevo dispositivo
      const newDeviceId = await Database.insert('devices', {
        device_id,
        device_name,
        launcher_version,
        android_version,
        model,
        manufacturer,
        brand,
        sdk_int,
        device_info: JSON.stringify(device_info || {}),
        ip_address: req.ip,
        status: 'active'
      });
      
      res.json({
        success: true,
        message: 'Dispositivo registrado',
        device_id: newDeviceId
      });
    }
    
  } catch (error) {
    console.error('Error registrando dispositivo:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// 💓 Heartbeat
router.post('/heartbeat', async (req, res) => {
  try {
    const { device_id } = req.body;
    
    if (!device_id) {
      return res.status(400).json({
        error: 'device_id es requerido'
      });
    }
    
    // Actualizar último heartbeat
    const updated = await Database.update('devices', {
      last_seen: new Date(),
      ip_address: req.ip,
      status: 'active'
    }, 'device_id = ?', [device_id]);
    
    if (updated === 0) {
      return res.status(404).json({
        error: 'Dispositivo no encontrado'
      });
    }
    
    res.json({
      success: true,
      message: 'Heartbeat recibido'
    });
    
  } catch (error) {
    console.error('Error en heartbeat:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// 📡 Obtener comandos pendientes
router.get('/commands', async (req, res) => {
  try {
    const { device_id } = req.query;
    
    if (!device_id) {
      return res.status(400).json({
        error: 'device_id es requerido'
      });
    }
    
    // Buscar dispositivo
    const device = await Database.findOne('devices', 'device_id = ?', [device_id]);
    if (!device) {
      return res.status(404).json({
        error: 'Dispositivo no encontrado'
      });
    }
    
    // Obtener comandos pendientes
    const commands = await Database.findMany(
      'remote_commands',
      'device_id = ? AND status = "pending"',
      [device.id],
      'created_at ASC'
    );
    
    res.json({
      success: true,
      commands: commands
    });
    
  } catch (error) {
    console.error('Error obteniendo comandos:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// ✅ Confirmar ejecución de comando
router.post('/command-ack', async (req, res) => {
  try {
    const { command_id, status, error_message } = req.body;
    
    if (!command_id || !status) {
      return res.status(400).json({
        error: 'command_id y status son requeridos'
      });
    }
    
    // Actualizar comando
    await Database.update('remote_commands', {
      status,
      executed_at: new Date(),
      error_message: error_message || null
    }, 'id = ?', [command_id]);
    
    res.json({
      success: true,
      message: 'Comando confirmado'
    });
    
  } catch (error) {
    console.error('Error confirmando comando:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

module.exports = router;
