import 'package:flutter/services.dart';

/// Servicio para cambiar el wallpaper del dispositivo
class WallpaperService {
  static final WallpaperService _instance = WallpaperService._internal();
  factory WallpaperService() => _instance;
  WallpaperService._internal();

  static const MethodChannel _channel = MethodChannel('ruralcare/wallpaper');

  /// Cambiar wallpaper desde una URL
  Future<bool> setWallpaperFromUrl(String imageUrl) async {
    try {
      print('🖼️ Cambiando wallpaper a: $imageUrl');
      
      // Validar URL
      if (imageUrl.isEmpty) {
        print('❌ URL de imagen vacía');
        return false;
      }

      // Verificar que sea una URL válida
      final uri = Uri.tryParse(imageUrl);
      if (uri == null || (!uri.scheme.startsWith('http'))) {
        print('❌ URL de imagen inválida: $imageUrl');
        return false;
      }

      // Llamar al método nativo
      final bool result = await _channel.invokeMethod('setWallpaper', {
        'imageUrl': imageUrl,
      });

      if (result) {
        print('✅ Wallpaper cambiado exitosamente');
      } else {
        print('❌ Error cambiando wallpaper');
      }

      return result;
    } catch (e) {
      print('❌ Excepción cambiando wallpaper: $e');
      return false;
    }
  }

  /// Cambiar wallpaper con URLs predefinidas para testing
  Future<bool> setTestWallpaper(String testType) async {
    String imageUrl;
    
    switch (testType.toLowerCase()) {
      case 'nature':
        imageUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop';
        break;
      case 'abstract':
        imageUrl = 'https://images.unsplash.com/photo-1557683316-973673baf926?w=1920&h=1080&fit=crop';
        break;
      case 'minimal':
        imageUrl = 'https://images.unsplash.com/photo-1557683304-673a23048d34?w=1920&h=1080&fit=crop';
        break;
      case 'blue':
        imageUrl = 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop';
        break;
      default:
        imageUrl = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop';
    }

    return await setWallpaperFromUrl(imageUrl);
  }

  /// Validar URL de imagen
  bool isValidImageUrl(String url) {
    if (url.isEmpty) return false;
    
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    // Verificar que sea HTTP/HTTPS
    if (!uri.scheme.startsWith('http')) return false;
    
    // Verificar extensiones de imagen comunes
    final path = uri.path.toLowerCase();
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp'];
    
    // Si no tiene extensión, asumir que es válida (puede ser un servicio como Unsplash)
    if (!path.contains('.')) return true;
    
    return validExtensions.any((ext) => path.endsWith(ext));
  }

  /// Obtener URLs de wallpapers predefinidos
  List<Map<String, String>> getPredefinedWallpapers() {
    return [
      {
        'name': 'Naturaleza',
        'description': 'Paisaje montañoso',
        'url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop',
        'thumbnail': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop',
      },
      {
        'name': 'Abstracto',
        'description': 'Diseño abstracto colorido',
        'url': 'https://images.unsplash.com/photo-1557683316-973673baf926?w=1920&h=1080&fit=crop',
        'thumbnail': 'https://images.unsplash.com/photo-1557683316-973673baf926?w=300&h=200&fit=crop',
      },
      {
        'name': 'Minimalista',
        'description': 'Diseño limpio y simple',
        'url': 'https://images.unsplash.com/photo-1557683304-673a23048d34?w=1920&h=1080&fit=crop',
        'thumbnail': 'https://images.unsplash.com/photo-1557683304-673a23048d34?w=300&h=200&fit=crop',
      },
      {
        'name': 'Azul Océano',
        'description': 'Tonos azules relajantes',
        'url': 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=1920&h=1080&fit=crop',
        'thumbnail': 'https://images.unsplash.com/photo-1419242902214-272b3f66ee7a?w=300&h=200&fit=crop',
      },
      {
        'name': 'Espacio',
        'description': 'Vista del espacio exterior',
        'url': 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=1920&h=1080&fit=crop',
        'thumbnail': 'https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=300&h=200&fit=crop',
      },
    ];
  }
}
