import React, { useState, useEffect } from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Tablet,
  CheckCircle,
  Block,
  SignalWifiOff,
  Send,
  Done
} from '@mui/icons-material';
import { statsService } from '../services/api';

const StatsCards = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await statsService.getStats();
      if (response.success) {
        setStats(response.stats);
      } else {
        setError('Error cargando estadísticas');
      }
    } catch (error) {
      setError('Error de conexión');
      console.error('Error cargando estadísticas:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const statsCards = [
    {
      title: 'Total Dispositivos',
      value: stats?.devices?.total || 0,
      icon: <Tablet />,
      color: '#2196F3',
      bgColor: 'rgba(33, 150, 243, 0.1)'
    },
    {
      title: 'Activos',
      value: stats?.devices?.active || 0,
      icon: <CheckCircle />,
      color: '#4CAF50',
      bgColor: 'rgba(76, 175, 80, 0.1)'
    },
    {
      title: 'Suspendidos',
      value: stats?.devices?.suspended || 0,
      icon: <Block />,
      color: '#FF9800',
      bgColor: 'rgba(255, 152, 0, 0.1)'
    },
    {
      title: 'Offline',
      value: stats?.devices?.offline || 0,
      icon: <SignalWifiOff />,
      color: '#F44336',
      bgColor: 'rgba(244, 67, 54, 0.1)'
    },
    {
      title: 'Comandos Pendientes',
      value: stats?.commands?.pending || 0,
      icon: <Send />,
      color: '#9C27B0',
      bgColor: 'rgba(156, 39, 176, 0.1)'
    },
    {
      title: 'Comandos Ejecutados',
      value: stats?.commands?.executed || 0,
      icon: <Done />,
      color: '#607D8B',
      bgColor: 'rgba(96, 125, 139, 0.1)'
    }
  ];

  return (
    <Grid container spacing={3}>
      {statsCards.map((card, index) => (
        <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
          <Card
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: 4,
              },
            }}
          >
            <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: 60,
                  height: 60,
                  borderRadius: '50%',
                  backgroundColor: card.bgColor,
                  margin: '0 auto 16px auto',
                  color: card.color,
                }}
              >
                {React.cloneElement(card.icon, { fontSize: 'large' })}
              </Box>
              
              <Typography
                variant="h4"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  color: card.color,
                  mb: 1,
                }}
              >
                {card.value}
              </Typography>
              
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  fontWeight: 500,
                }}
              >
                {card.title}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default StatsCards;
