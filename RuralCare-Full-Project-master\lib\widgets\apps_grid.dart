import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_strings.dart';
import '../models/app_item.dart';
import '../models/app_category.dart';
import '../services/app_manager_service.dart';
import '../widgets/category_tabs_widget.dart';

/// Grilla de aplicaciones optimizada para personas mayores
/// Muestra aplicaciones con iconos grandes y texto claro
class AppsGrid extends StatefulWidget {
  final Function(String)? onAppTap; // Opcional, usa el servicio por defecto

  const AppsGrid({
    super.key,
    this.onAppTap,
  });

  @override
  State<AppsGrid> createState() => _AppsGridState();
}

class _AppsGridState extends State<AppsGrid> {
  List<AppItem> _favoriteApps = [];
  List<AppItem> _filteredApps = [];
  bool _isLoading = true;
  String _selectedCategoryId = 'favorites';

  @override
  void initState() {
    super.initState();
    _loadFavoriteApps();
  }

  void _onCategoryChanged(String categoryId) {
    setState(() {
      _selectedCategoryId = categoryId;
      _filterAppsByCategory();
    });
  }

  void _filterAppsByCategory() {
    if (_selectedCategoryId == 'all') {
      _filteredApps = _favoriteApps.where((app) => !app.isHidden).toList();
    } else {
      _filteredApps = AppCategory.filterAppsByCategory(_favoriteApps, _selectedCategoryId)
          .where((app) => !app.isHidden)
          .toList();
    }
  }

  void _loadFavoriteApps() async {
    try {
      print('📱 Cargando aplicaciones favoritas...');
      final apps = await AppManagerService.instance.getFavoriteApps();

      if (mounted) {
        setState(() {
          _favoriteApps = apps;
          _isLoading = false;
          _filterAppsByCategory();
        });
        print('✅ Cargadas ${apps.length} aplicaciones favoritas');
      }
    } catch (e) {
      print('❌ Error cargando apps favoritas: $e');
      // Fallback a apps por defecto
      if (mounted) {
        setState(() {
          _favoriteApps = _getDefaultApps();
        });
      }
    }
  }

  /// Apps por defecto en caso de error
  List<AppItem> _getDefaultApps() {
    return [
      AppItem(
        name: AppStrings.phone,
        packageName: 'com.android.dialer',
        icon: Icons.phone,
        color: AppColors.success,
      ),
      AppItem(
        name: AppStrings.messages,
        packageName: 'com.android.mms',
        icon: Icons.sms,
        color: AppColors.primary,
      ),
      AppItem(
        name: AppStrings.camera,
        packageName: 'com.android.camera2',
        icon: Icons.camera_alt,
        color: AppColors.info,
      ),
      AppItem(
        name: 'WhatsApp',
        packageName: 'com.whatsapp',
        icon: Icons.chat,
        color: AppColors.success,
      ),
      AppItem(
        name: 'YouTube',
        packageName: 'com.google.android.youtube',
        icon: Icons.play_circle_fill,
        color: AppColors.error,
      ),
      AppItem(
        name: 'Gmail',
        packageName: 'com.google.android.gm',
        icon: Icons.mail,
        color: AppColors.error,
      ),
      AppItem(
        name: AppStrings.contacts,
        packageName: 'com.android.contacts',
        icon: Icons.contacts,
        color: AppColors.success,
      ),
      AppItem(
        name: AppStrings.music,
        packageName: 'com.android.music',
        icon: Icons.music_note,
        color: AppColors.adminPrimary,
      ),
   
      AppItem(
        name: AppStrings.settings,
        packageName: 'com.android.settings',
        icon: Icons.settings,
        color: AppColors.textSecondary,
      ),
    ];
  }

  /// Manejar el tap en una aplicación
  void _handleAppTap(String packageName) async {
    try {
      // Usar callback personalizado si existe, sino usar el servicio
      if (widget.onAppTap != null) {
        widget.onAppTap!(packageName);
      } else {
        print('🚀 Lanzando aplicación: $packageName');
        final success = await AppManagerService.instance.launchApp(packageName);

        if (!success) {
          // Mostrar mensaje de error si la app no se puede abrir
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('No se pudo abrir la aplicación'),
                backgroundColor: AppColors.error,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        }
      }
    } catch (e) {
      print('❌ Error al abrir aplicación $packageName: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al abrir la aplicación'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Determinar número de columnas según el tamaño de pantalla
    int crossAxisCount;
    double childAspectRatio;
    double spacing;
    EdgeInsets padding;

    if (screenWidth > 1200) {
      // Tablets muy grandes o pantallas de escritorio
      crossAxisCount = 6;
      childAspectRatio = 0.9;
      spacing = AppDimensions.paddingL;
      padding = const EdgeInsets.all(AppDimensions.paddingXL);
    } else if (screenWidth > 900) {
      // Tablets grandes
      crossAxisCount = 5;
      childAspectRatio = 0.95;
      spacing = AppDimensions.paddingM;
      padding = const EdgeInsets.all(AppDimensions.paddingL);
    } else if (screenWidth > 600) {
      // Tablets medianas
      crossAxisCount = 4;
      childAspectRatio = 1.0;
      spacing = AppDimensions.paddingM;
      padding = const EdgeInsets.all(AppDimensions.paddingM);
    } else {
      // Teléfonos o tablets pequeñas
      crossAxisCount = 3;
      childAspectRatio = 1.1;
      spacing = AppDimensions.paddingS;
      padding = const EdgeInsets.all(AppDimensions.paddingS);
    }

    return Column(
      children: [
        // Pestañas de categorías
        if (_favoriteApps.isNotEmpty)
          CategoryTabsWidget(
            apps: _favoriteApps,
            onCategoryChanged: _onCategoryChanged,
            selectedCategoryId: _selectedCategoryId,
          ),

        const SizedBox(height: 16),

        // Grilla de aplicaciones
        Expanded(
          child: _isLoading
              ? _buildLoadingState()
              : _filteredApps.isEmpty
                  ? _buildEmptyState()
                  : GridView.builder(
                      padding: padding,
                      physics: const BouncingScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        crossAxisSpacing: spacing,
                        mainAxisSpacing: spacing,
                        childAspectRatio: childAspectRatio,
                      ),
                      itemCount: _filteredApps.length,
                      itemBuilder: (context, index) {
                        final app = _filteredApps[index];
                        final isTablet = screenWidth > 600;
                        return _buildAppCard(app, isTablet);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Cargando aplicaciones...',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.apps,
            size: 64,
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No hay aplicaciones en esta categoría',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Prueba seleccionando otra categoría',
            style: TextStyle(
              color: AppColors.textSecondary.withOpacity(0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAppCard(AppItem app, [bool isTablet = false]) {
    return GestureDetector(
      onTap: () => _handleAppTap(app.packageName),
      child: Container(
        decoration: BoxDecoration(
          gradient: AppColors.surfaceGradient,
          borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.25),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            BoxShadow(
              color: app.color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: app.color.withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
            onTap: () => _handleAppTap(app.packageName),
            splashColor: app.color.withValues(alpha: 0.2),
            highlightColor: app.color.withValues(alpha: 0.1),
            child: Padding(
              padding: EdgeInsets.all(
                isTablet ? AppDimensions.paddingL : AppDimensions.paddingM,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icono de la aplicación con gradiente
                  Expanded(
                    flex: 4,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            app.color.withValues(alpha: 0.8),
                            app.color,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(
                          isTablet ? AppDimensions.radiusXL : AppDimensions.radiusL,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: app.color.withValues(alpha: 0.3),
                            blurRadius: isTablet ? 8 : 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        app.icon,
                        size: isTablet ? 48 : AppDimensions.iconXL,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  SizedBox(height: isTablet ? AppDimensions.paddingS : AppDimensions.paddingXS),

                  // Nombre de la aplicación
                  Text(
                    app.name,
                    style: TextStyle(
                      fontSize: isTablet ? AppDimensions.fontM : AppDimensions.fontS,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                      shadows: const [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
