import axios from 'axios';

// Configuración base de la API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

// Crear instancia de axios
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para agregar token de autenticación
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para manejar respuestas
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expirado o inválido
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 🔐 Servicios de autenticación
export const authService = {
  async login(username, password) {
    const response = await api.post('/auth/login', { username, password });
    if (response.data.success) {
      localStorage.setItem('authToken', response.data.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.data.user));
    }
    return response.data;
  },

  async logout() {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Error en logout:', error);
    } finally {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    }
  },

  async getCurrentUser() {
    const response = await api.get('/auth/me');
    return response.data;
  },

  isAuthenticated() {
    return !!localStorage.getItem('authToken');
  },

  getUser() {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }
};

// 📱 Servicios de dispositivos
export const deviceService = {
  async getDevices() {
    const response = await api.get('/admin/devices');
    return response.data;
  },

  async suspendDevice(deviceId) {
    const response = await api.post(`/admin/devices/${deviceId}/suspend`);
    return response.data;
  },

  async activateDevice(deviceId) {
    const response = await api.post(`/admin/devices/${deviceId}/activate`);
    return response.data;
  },

  async sendCommand(deviceId, commandType, payload = {}) {
    const response = await api.post(`/admin/devices/${deviceId}/command`, {
      command_type: commandType,
      payload
    });
    return response.data;
  }
};

// 📊 Servicios de estadísticas
export const statsService = {
  async getStats() {
    const response = await api.get('/admin/stats');
    return response.data;
  },

  async getPublicStats() {
    const response = await api.get('/public-stats');
    return response.data;
  }
};

// 🔍 Servicio de prueba
export const testService = {
  async testConnection() {
    const response = await api.get('/test');
    return response.data;
  }
};

export default api;
