import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';

/// Servicio para configurar la pantalla de bloqueo personalizada
class LockScreenConfigService {
  static final LockScreenConfigService _instance = LockScreenConfigService._internal();
  static LockScreenConfigService get instance => _instance;
  LockScreenConfigService._internal();

  static const MethodChannel _channel = MethodChannel('ruralcare/lock_screen_config');
  
  // Claves para SharedPreferences
  static const String _backgroundImageKey = 'lock_screen_background_image';
  static const String _logoImageKey = 'lock_screen_logo_image';
  static const String _customTextKey = 'lock_screen_custom_text';
  static const String _textColorKey = 'lock_screen_text_color';
  static const String _logoPositionKey = 'lock_screen_logo_position';
  static const String _textPositionKey = 'lock_screen_text_position';
  static const String _enabledKey = 'lock_screen_custom_enabled';

  SharedPreferences? _prefs;

  /// Inicializar el servicio
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      print('✅ LockScreenConfigService inicializado');
    } catch (e) {
      print('❌ Error inicializando LockScreenConfigService: $e');
    }
  }

  /// Seleccionar imagen de fondo personalizada
  Future<String?> selectBackgroundImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        // Guardar imagen en directorio de la app
        final Directory appDir = await getApplicationDocumentsDirectory();
        final String fileName = 'lock_screen_bg_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final String localPath = '${appDir.path}/$fileName';
        
        // Copiar imagen seleccionada
        await File(image.path).copy(localPath);
        
        // Guardar ruta en preferencias
        await _prefs?.setString(_backgroundImageKey, localPath);
        
        print('✅ Imagen de fondo guardada: $localPath');
        return localPath;
      }
      
      return null;
    } catch (e) {
      print('❌ Error seleccionando imagen de fondo: $e');
      return null;
    }
  }

  /// Seleccionar logo personalizado
  Future<String?> selectLogoImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 90,
      );

      if (image != null) {
        // Guardar logo en directorio de la app
        final Directory appDir = await getApplicationDocumentsDirectory();
        final String fileName = 'lock_screen_logo_${DateTime.now().millisecondsSinceEpoch}.png';
        final String localPath = '${appDir.path}/$fileName';
        
        // Copiar imagen seleccionada
        await File(image.path).copy(localPath);
        
        // Guardar ruta en preferencias
        await _prefs?.setString(_logoImageKey, localPath);
        
        print('✅ Logo guardado: $localPath');
        return localPath;
      }
      
      return null;
    } catch (e) {
      print('❌ Error seleccionando logo: $e');
      return null;
    }
  }

  /// Configurar texto personalizado
  Future<void> setCustomText(String text) async {
    try {
      await _prefs?.setString(_customTextKey, text);
      print('✅ Texto personalizado guardado: $text');
    } catch (e) {
      print('❌ Error guardando texto personalizado: $e');
    }
  }

  /// Configurar color del texto
  Future<void> setTextColor(Color color) async {
    try {
      await _prefs?.setInt(_textColorKey, color.value);
      print('✅ Color de texto guardado: ${color.value}');
    } catch (e) {
      print('❌ Error guardando color de texto: $e');
    }
  }

  /// Configurar posición del logo
  Future<void> setLogoPosition(String position) async {
    try {
      await _prefs?.setString(_logoPositionKey, position);
      print('✅ Posición de logo guardada: $position');
    } catch (e) {
      print('❌ Error guardando posición de logo: $e');
    }
  }

  /// Configurar posición del texto
  Future<void> setTextPosition(String position) async {
    try {
      await _prefs?.setString(_textPositionKey, position);
      print('✅ Posición de texto guardada: $position');
    } catch (e) {
      print('❌ Error guardando posición de texto: $e');
    }
  }

  /// Habilitar/deshabilitar configuración personalizada
  Future<void> setCustomConfigEnabled(bool enabled) async {
    try {
      await _prefs?.setBool(_enabledKey, enabled);
      print('✅ Configuración personalizada ${enabled ? 'habilitada' : 'deshabilitada'}');
    } catch (e) {
      print('❌ Error configurando estado: $e');
    }
  }

  /// Obtener configuración actual
  Future<Map<String, dynamic>> getCurrentConfig() async {
    try {
      return {
        'backgroundImage': _prefs?.getString(_backgroundImageKey),
        'logoImage': _prefs?.getString(_logoImageKey),
        'customText': _prefs?.getString(_customTextKey) ?? '',
        'textColor': Color(_prefs?.getInt(_textColorKey) ?? 0xFFFFFFFF),
        'logoPosition': _prefs?.getString(_logoPositionKey) ?? 'top-center',
        'textPosition': _prefs?.getString(_textPositionKey) ?? 'bottom-center',
        'enabled': _prefs?.getBool(_enabledKey) ?? false,
      };
    } catch (e) {
      print('❌ Error obteniendo configuración: $e');
      return {};
    }
  }

  /// Aplicar configuración a la pantalla de bloqueo del sistema
  Future<bool> applyConfiguration() async {
    try {
      final config = await getCurrentConfig();
      
      if (!config['enabled']) {
        print('⚠️ Configuración personalizada deshabilitada');
        return false;
      }

      // Enviar configuración al canal nativo
      final bool result = await _channel.invokeMethod('applyLockScreenConfig', config);
      
      if (result) {
        print('✅ Configuración aplicada exitosamente');
      } else {
        print('❌ Error aplicando configuración');
      }
      
      return result;
    } catch (e) {
      print('❌ Error aplicando configuración: $e');
      return false;
    }
  }

  /// Resetear configuración a valores por defecto
  Future<void> resetConfiguration() async {
    try {
      await _prefs?.remove(_backgroundImageKey);
      await _prefs?.remove(_logoImageKey);
      await _prefs?.remove(_customTextKey);
      await _prefs?.remove(_textColorKey);
      await _prefs?.remove(_logoPositionKey);
      await _prefs?.remove(_textPositionKey);
      await _prefs?.remove(_enabledKey);
      
      print('✅ Configuración reseteada');
    } catch (e) {
      print('❌ Error reseteando configuración: $e');
    }
  }

  /// Obtener imagen como bytes para vista previa
  Future<Uint8List?> getImageBytes(String? imagePath) async {
    try {
      if (imagePath == null || imagePath.isEmpty) return null;
      
      final File file = File(imagePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      
      return null;
    } catch (e) {
      print('❌ Error leyendo imagen: $e');
      return null;
    }
  }
}
