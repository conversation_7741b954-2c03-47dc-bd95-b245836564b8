const mysql = require('mysql2/promise');

async function debugSuspend() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'senior_launcher'
    });
    
    console.log('🔍 Debug de suspensión...');
    
    // 1. Verificar dispositivos
    const [devices] = await connection.execute(`
      SELECT id, device_id, device_name, status 
      FROM devices 
      ORDER BY last_seen DESC
    `);
    
    console.log('\n📱 Dispositivos:');
    devices.forEach((device, index) => {
      console.log(`${index + 1}. ${device.device_name || 'Sin nombre'}`);
      console.log(`   UUID: ${device.id}`);
      console.log(`   Device ID: ${device.device_id}`);
      console.log(`   Estado: ${device.status}`);
    });
    
    // 2. Verificar todos los comandos (no solo pendientes)
    const [allCommands] = await connection.execute(`
      SELECT id, device_id, command_type, status, created_at, payload 
      FROM remote_commands 
      ORDER BY created_at DESC 
      LIMIT 20
    `);
    
    console.log(`\n📨 Últimos 20 comandos:`);
    allCommands.forEach((cmd, index) => {
      console.log(`\n${index + 1}. ${cmd.command_type} (${cmd.status})`);
      console.log(`   ID: ${cmd.id}`);
      console.log(`   Device: ${cmd.device_id}`);
      console.log(`   Creado: ${cmd.created_at}`);
      if (cmd.payload) {
        console.log(`   Payload: ${cmd.payload}`);
      }
    });
    
    // 3. Verificar comandos de suspensión específicamente
    const [suspendCommands] = await connection.execute(`
      SELECT id, device_id, command_type, status, created_at, payload 
      FROM remote_commands 
      WHERE command_type IN ('suspend', 'activate')
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`\n🚫 Comandos de suspensión/activación: ${suspendCommands.length}`);
    suspendCommands.forEach((cmd, index) => {
      console.log(`\n${index + 1}. ${cmd.command_type} (${cmd.status})`);
      console.log(`   Device: ${cmd.device_id}`);
      console.log(`   Creado: ${cmd.created_at}`);
      console.log(`   Payload: ${cmd.payload}`);
    });
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugSuspend();
