import 'package:flutter/services.dart';
import 'package:volume_controller/volume_controller.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Servicio para manejar configuraciones del sistema
class SystemSettingsService {
  static final SystemSettingsService _instance = SystemSettingsService._internal();
  static SystemSettingsService get instance => _instance;
  SystemSettingsService._internal();

  // Claves para SharedPreferences
  static const String _volumeKey = 'system_volume';
  static const String _brightnessKey = 'system_brightness';
  static const String _fontSizeKey = 'font_size_scale';
  static const String _wifiEnabledKey = 'wifi_enabled';
  static const String _bluetoothEnabledKey = 'bluetooth_enabled';
  static const String _autoRotateKey = 'auto_rotate_enabled';

  SharedPreferences? _prefs;

  /// Inicializar el servicio
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      // Configurar el controlador de volumen
      VolumeController().showSystemUI = false;

      print('✅ SystemSettingsService inicializado correctamente');
    } catch (e) {
      print('❌ Error inicializando SystemSettingsService: $e');
    }
  }

  /// Obtener el volumen actual (0.0 - 1.0)
  Future<double> getVolume() async {
    try {
      final volume = await VolumeController().getVolume();
      return volume;
    } catch (e) {
      print('Error obteniendo volumen: $e');
      return _prefs?.getDouble(_volumeKey) ?? 0.7;
    }
  }

  /// Establecer el volumen (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    try {
      VolumeController().setVolume(volume);
      await _prefs?.setDouble(_volumeKey, volume);
    } catch (e) {
      print('Error estableciendo volumen: $e');
    }
  }

  /// Obtener el brillo actual (0.0 - 1.0)
  Future<double> getBrightness() async {
    try {
      final brightness = await ScreenBrightness().current;
      return brightness;
    } catch (e) {
      print('Error obteniendo brillo: $e');
      return _prefs?.getDouble(_brightnessKey) ?? 0.8;
    }
  }

  /// Establecer el brillo (0.0 - 1.0)
  Future<void> setBrightness(double brightness) async {
    try {
      await ScreenBrightness().setScreenBrightness(brightness);
      await _prefs?.setDouble(_brightnessKey, brightness);
    } catch (e) {
      print('Error estableciendo brillo: $e');
    }
  }

  /// Obtener la escala de fuente (0.5 - 2.0)
  double getFontScale() {
    return _prefs?.getDouble(_fontSizeKey) ?? 1.0;
  }

  /// Establecer la escala de fuente (0.5 - 2.0)
  Future<void> setFontScale(double scale) async {
    await _prefs?.setDouble(_fontSizeKey, scale);
  }

  /// Obtener estado de Wi-Fi
  bool getWifiEnabled() {
    return _prefs?.getBool(_wifiEnabledKey) ?? true;
  }

  /// Establecer estado de Wi-Fi
  Future<void> setWifiEnabled(bool enabled) async {
    await _prefs?.setBool(_wifiEnabledKey, enabled);
    // TODO: Implementar control real de Wi-Fi usando connectivity_plus
    print('Wi-Fi ${enabled ? 'habilitado' : 'deshabilitado'}');
  }

  /// Obtener estado de Bluetooth
  bool getBluetoothEnabled() {
    return _prefs?.getBool(_bluetoothEnabledKey) ?? false;
  }

  /// Establecer estado de Bluetooth
  Future<void> setBluetoothEnabled(bool enabled) async {
    await _prefs?.setBool(_bluetoothEnabledKey, enabled);
    // TODO: Implementar control real de Bluetooth
    print('Bluetooth ${enabled ? 'habilitado' : 'deshabilitado'}');
  }

  /// Obtener estado de rotación automática
  bool getAutoRotateEnabled() {
    return _prefs?.getBool(_autoRotateKey) ?? true;
  }

  /// Establecer estado de rotación automática
  Future<void> setAutoRotateEnabled(bool enabled) async {
    await _prefs?.setBool(_autoRotateKey, enabled);
    
    try {
      if (enabled) {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      } else {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      }
    } catch (e) {
      print('Error configurando orientación: $e');
    }
  }

  /// Abrir configuración de Wi-Fi del sistema
  Future<void> openWifiSettings() async {
    try {
      const platform = MethodChannel('ruralcare/settings');
      await platform.invokeMethod('openWifiSettings');
    } catch (e) {
      print('Error abriendo configuración Wi-Fi: $e');
    }
  }

  /// Abrir configuración de Bluetooth del sistema
  Future<void> openBluetoothSettings() async {
    try {
      const platform = MethodChannel('ruralcare/settings');
      await platform.invokeMethod('openBluetoothSettings');
    } catch (e) {
      print('Error abriendo configuración Bluetooth: $e');
    }
  }
}
