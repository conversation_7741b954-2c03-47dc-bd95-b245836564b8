#!/usr/bin/env node

/**
 * Script para verificar y arreglar el usuario admin
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'senior_launcher',
  charset: 'utf8mb4'
};

async function fixAdminUser() {
  let connection;
  
  try {
    console.log('🔌 Conectando a MySQL...');
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ Conectado correctamente');

    // Verificar usuario admin actual
    console.log('🔍 Verificando usuario admin...');
    const [users] = await connection.execute(
      'SELECT id, username, email, password_hash, role FROM admin_users WHERE username = ?',
      ['admin']
    );

    if (users.length === 0) {
      console.log('❌ Usuario admin no encontrado. Creando...');
      
      // Crear hash de la contraseña
      const passwordHash = await bcrypt.hash('admin123', 10);
      
      // Insertar usuario admin
      await connection.execute(`
        INSERT INTO admin_users (username, email, password_hash, role, is_active) 
        VALUES (?, ?, ?, ?, ?)
      `, ['admin', '<EMAIL>', passwordHash, 'admin', true]);
      
      console.log('✅ Usuario admin creado correctamente');
    } else {
      console.log('✅ Usuario admin encontrado:', users[0].username);
      
      // Verificar si la contraseña es correcta
      const isValidPassword = await bcrypt.compare('admin123', users[0].password_hash);
      
      if (!isValidPassword) {
        console.log('❌ Contraseña incorrecta. Actualizando...');
        
        // Crear nuevo hash
        const newPasswordHash = await bcrypt.hash('admin123', 10);
        
        // Actualizar contraseña
        await connection.execute(
          'UPDATE admin_users SET password_hash = ? WHERE username = ?',
          [newPasswordHash, 'admin']
        );
        
        console.log('✅ Contraseña actualizada correctamente');
      } else {
        console.log('✅ Contraseña es correcta');
      }
    }

    // Mostrar información del usuario
    const [finalUsers] = await connection.execute(
      'SELECT username, email, role, is_active, created_at FROM admin_users WHERE username = ?',
      ['admin']
    );

    console.log('\n📋 Información del usuario admin:');
    console.log('  Usuario:', finalUsers[0].username);
    console.log('  Email:', finalUsers[0].email);
    console.log('  Rol:', finalUsers[0].role);
    console.log('  Activo:', finalUsers[0].is_active ? 'Sí' : 'No');
    console.log('  Creado:', finalUsers[0].created_at);

    console.log('\n🔐 Credenciales para el panel web:');
    console.log('  Usuario: admin');
    console.log('  Contraseña: admin123');
    console.log('  URL: http://localhost:3001');

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Función para probar login
async function testLogin() {
  try {
    console.log('🧪 Probando login...');
    
    const response = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Login exitoso!');
      console.log('Token:', data.data.token.substring(0, 20) + '...');
      console.log('Usuario:', data.data.user.username);
    } else {
      console.log('❌ Login falló:', data.error);
    }

  } catch (error) {
    console.error('❌ Error probando login:', error.message);
  }
}

// Ejecutar según argumentos
const command = process.argv[2];

switch (command) {
  case 'test':
    testLogin();
    break;
  case 'fix':
  default:
    fixAdminUser();
    break;
}
