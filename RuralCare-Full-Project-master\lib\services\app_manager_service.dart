import 'package:flutter/material.dart';
import 'package:installed_apps/installed_apps.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_item.dart';
import '../models/app_category.dart';
import '../constants/app_colors.dart';
import 'kiosk_handoff_service.dart';

/// Servicio para gestionar aplicaciones instaladas y favoritas
class AppManagerService {
  static AppManagerService? _instance;
  static AppManagerService get instance => _instance ??= AppManagerService._();
  
  AppManagerService._();

  // Cache de aplicaciones
  List<AppItem> _installedApps = [];
  List<AppItem> _favoriteApps = [];
  
  /// Apps populares predefinidas con package names comunes
  static const Map<String, AppData> _popularApps = {
    // Comunicación
    'com.whatsapp': AppData('WhatsApp', Icons.chat, AppColors.success),
    'com.facebook.orca': AppData('Messenger', Icons.message, AppColors.primary),
    'com.skype.raider': AppData('Skype', Icons.video_call, AppColors.info),
    'com.viber.voip': AppData('Viber', Icons.phone, AppColors.accent),
    'org.telegram.messenger': AppData('Telegram', Icons.send, AppColors.primary),
    
    // Redes Sociales
    'com.facebook.katana': AppData('Facebook', Icons.facebook, AppColors.primary),
    'com.instagram.android': AppData('Instagram', Icons.camera_alt, AppColors.accent),
    'com.twitter.android': AppData('Twitter', Icons.alternate_email, AppColors.info),
    'com.linkedin.android': AppData('LinkedIn', Icons.work, AppColors.primary),
    
    // Entretenimiento
    'com.google.android.youtube': AppData('YouTube', Icons.play_circle_fill, AppColors.error),
    'com.netflix.mediaclient': AppData('Netflix', Icons.movie, AppColors.error),
    'com.spotify.music': AppData('Spotify', Icons.music_note, AppColors.success),
    'com.amazon.avod.thirdpartyclient': AppData('Prime Video', Icons.tv, AppColors.info),
    'com.disney.disneyplus': AppData('Disney+', Icons.star, AppColors.primary),
    
    // Productividad
    'com.microsoft.office.outlook': AppData('Outlook', Icons.email, AppColors.primary),
    'com.google.android.gm': AppData('Gmail', Icons.mail, AppColors.error),
    'com.microsoft.office.word': AppData('Word', Icons.description, AppColors.primary),
    'com.adobe.reader': AppData('PDF Reader', Icons.picture_as_pdf, AppColors.error),
    'com.dropbox.android': AppData('Dropbox', Icons.cloud, AppColors.primary),
    
    // Navegación y Mapas
    'com.google.android.apps.maps': AppData('Maps', Icons.map, AppColors.success),
    'com.waze': AppData('Waze', Icons.navigation, AppColors.info),
    'com.uber.app': AppData('Uber', Icons.local_taxi, AppColors.textPrimary),
    
    // Compras y Finanzas
    'com.amazon.mShop.android.shopping': AppData('Amazon', Icons.shopping_cart, AppColors.warning),
    'com.paypal.android.p2pmobile': AppData('PayPal', Icons.payment, AppColors.primary),
    'com.mercadolibre': AppData('MercadoLibre', Icons.store, AppColors.warning),
    
    // Salud y Fitness
    'com.fitbit.FitbitMobile': AppData('Fitbit', Icons.fitness_center, AppColors.success),
    'com.samsung.android.app.shealth': AppData('Samsung Health', Icons.favorite, AppColors.error),
    'com.google.android.apps.fitness': AppData('Google Fit', Icons.directions_run, AppColors.success),
    
    // Utilidades
    'com.google.android.calculator': AppData('Calculadora', Icons.calculate, AppColors.textPrimary),
    'com.android.chrome': AppData('Chrome', Icons.web, AppColors.primary),
    'com.google.android.apps.photos': AppData('Fotos', Icons.photo_library, AppColors.success),
    'com.android.settings': AppData('Configuración', Icons.settings, AppColors.textSecondary),
    
    // Apps del sistema Android comunes
    'com.android.dialer': AppData('Teléfono', Icons.phone, AppColors.success),
    'com.android.mms': AppData('Mensajes', Icons.sms, AppColors.primary),
    'com.android.camera2': AppData('Cámara', Icons.camera_alt, AppColors.info),
    'com.android.contacts': AppData('Contactos', Icons.contacts, AppColors.accent),
    'com.android.calendar': AppData('Calendario', Icons.calendar_today, AppColors.error),
    'com.android.email': AppData('Email', Icons.email, AppColors.primary),
    'com.android.music': AppData('Música', Icons.music_note, AppColors.accent),
    'com.android.gallery3d': AppData('Galería', Icons.photo, AppColors.success),

    // Más aplicaciones comunes que pueden estar instaladas
    'com.google.android.apps.messaging': AppData('Mensajes', Icons.message, AppColors.primary),
    'com.google.android.dialer': AppData('Teléfono Google', Icons.phone, AppColors.success),
    'com.google.android.music': AppData('YouTube Music', Icons.music_note, AppColors.error),
    'com.google.android.apps.docs': AppData('Google Docs', Icons.description, AppColors.primary),
    'com.google.android.apps.sheets': AppData('Google Sheets', Icons.grid_on, AppColors.success),
    'com.google.android.apps.slides': AppData('Google Slides', Icons.slideshow, AppColors.warning),
    'com.google.android.apps.drive': AppData('Google Drive', Icons.cloud, AppColors.primary),
    'com.google.android.keep': AppData('Google Keep', Icons.note, AppColors.warning),
    'com.google.android.apps.translate': AppData('Google Translate', Icons.translate, AppColors.primary),
    'com.android.vending': AppData('Play Store', Icons.shop, AppColors.success),
    'com.google.android.apps.nbu.files': AppData('Files by Google', Icons.folder_open, AppColors.primary),
    'com.android.filemanager': AppData('Archivos', Icons.folder, AppColors.info),
    'com.opera.browser': AppData('Opera', Icons.web, AppColors.error),
    'com.microsoft.emmx': AppData('Edge', Icons.web, AppColors.primary),
    'org.mozilla.firefox': AppData('Firefox', Icons.web, AppColors.warning),
    'com.amazon.venezia': AppData('Amazon Appstore', Icons.store, AppColors.warning),
  };

  /// Determinar si una app del sistema debe ocultarse
  bool _isSystemAppToHide(String packageName) {
    // Lista de apps del sistema que queremos ocultar
    final systemAppsToHide = [
      // Launchers del sistema
      'com.android.launcher',
      'com.android.launcher2',
      'com.android.launcher3',
      'com.google.android.apps.nexuslauncher',
      'com.samsung.android.app.launcher',
      'com.huawei.android.launcher',

      // Teclados del sistema
      'com.android.inputmethod',
      'com.google.android.inputmethod',
      'com.samsung.android.honeyboard',

      // Apps del sistema muy técnicas
      'com.android.systemui',
      'com.android.settings.intelligence',
      'com.android.providers',
      'com.android.server',
      'android.auto_generated_rro',
      'com.android.shell',
      'com.android.sharedstoragebackup',
      'com.android.statementservice',
      'com.android.storagemanager',
      'com.android.externalstorage',
      'com.android.htmlviewer',
      'com.android.companiondevicemanager',
      'com.android.mtp',
      'com.android.nfc',
      'com.android.se',
      'com.android.wallpaper.livepicker',
      'com.android.dreams.basic',
      'com.android.dreams.phototable',
      'com.android.printspooler',
      'com.android.proxyhandler',
      'com.android.pacprocessor',
      'com.android.certinstaller',
      'com.android.carrierconfig',
      'com.android.managedprovisioning',
      'com.android.bips',
      'com.android.bookmarkprovider',
      'com.android.calllogbackup',
      'com.android.keychain',
      'com.android.localtransport',
      'com.android.wallpaperbackup',
    ];

    // Verificar si el package name está en la lista de exclusión
    for (final hiddenApp in systemAppsToHide) {
      if (packageName.startsWith(hiddenApp)) {
        return true;
      }
    }

    // Patrones adicionales a ocultar
    if (packageName.contains('.test') ||
        packageName.contains('.stub') ||
        packageName.contains('com.android.cts') ||
        packageName.contains('com.google.android.overlay') ||
        packageName.endsWith('.overlay')) {
      return true;
    }

    return false;
  }

  /// Obtener todas las aplicaciones instaladas
  Future<List<AppItem>> getInstalledApps() async {
    try {
      print('🔍 Escaneando aplicaciones instaladas...');
      
      // Obtener apps instaladas del sistema
      final apps = await InstalledApps.getInstalledApps(true, true);
      
      _installedApps = apps.map((app) {
        // Buscar en apps populares primero
        final popularApp = _popularApps[app.packageName];
        
        if (popularApp != null) {
          return AppItem(
            id: app.packageName ?? '',
            name: popularApp.name,
            packageName: app.packageName ?? '',
            category: AppCategory.getCategoryForApp(app.packageName ?? '') ?? AppCategory.defaultCategories.last,
            icon: popularApp.icon,
            color: popularApp.color,
            isSystemApp: false, // Las apps populares no son del sistema
            appIcon: app.icon, // Icono real de la app
          );
        } else {
          // App no popular, usar datos del sistema
          return AppItem(
            id: app.packageName ?? '',
            name: app.name ?? 'App Desconocida',
            packageName: app.packageName ?? '',
            category: AppCategory.getCategoryForApp(app.packageName ?? '') ?? AppCategory.defaultCategories.last,
            icon: Icons.android, // Icono genérico
            color: AppColors.textSecondary,
            isSystemApp: false, // Por defecto no es del sistema
            appIcon: app.icon,
          );
        }
      }).where((app) =>
        app.packageName.isNotEmpty &&
        !_isSystemAppToHide(app.packageName)
      ).toList();

      // Ordenar por popularidad (apps conocidas primero)
      _installedApps.sort((a, b) {
        final aIsPopular = _popularApps.containsKey(a.packageName);
        final bIsPopular = _popularApps.containsKey(b.packageName);
        
        if (aIsPopular && !bIsPopular) return -1;
        if (!aIsPopular && bIsPopular) return 1;
        return a.name.compareTo(b.name);
      });

      print('✅ Encontradas ${_installedApps.length} aplicaciones');
      return _installedApps;
      
    } catch (e) {
      print('❌ Error obteniendo apps instaladas: $e');
      return _getDefaultApps();
    }
  }

  /// Obtener aplicaciones favoritas configuradas
  Future<List<AppItem>> getFavoriteApps() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritePackages = prefs.getStringList('favorite_apps') ?? [];
      
      if (favoritePackages.isEmpty) {
        // Primera vez, usar apps por defecto
        return _getDefaultApps();
      }
      
      // Asegurar que tenemos las apps instaladas
      if (_installedApps.isEmpty) {
        await getInstalledApps();
      }
      
      _favoriteApps = favoritePackages
          .map((packageName) => _installedApps.firstWhere(
                (app) => app.packageName == packageName,
                orElse: () => _getDefaultAppByPackage(packageName),
              ))
          .where((app) => app.packageName.isNotEmpty)
          .toList();
      
      return _favoriteApps;
      
    } catch (e) {
      print('❌ Error obteniendo apps favoritas: $e');
      return _getDefaultApps();
    }
  }

  /// Guardar aplicaciones favoritas
  Future<void> saveFavoriteApps(List<AppItem> apps) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final packageNames = apps.map((app) => app.packageName).toList();
      await prefs.setStringList('favorite_apps', packageNames);
      _favoriteApps = apps;
      print('✅ Apps favoritas guardadas: ${packageNames.length}');
    } catch (e) {
      print('❌ Error guardando apps favoritas: $e');
    }
  }

  /// Lanzar una aplicación con Kiosk Handoff inteligente
  Future<bool> launchApp(String packageName) async {
    try {
      print('🚀 Lanzando app: $packageName');

      // Buscar el nombre de la app
      String appName = packageName;
      final popularApp = _popularApps[packageName];
      if (popularApp != null) {
        appName = popularApp.name;
      }

      // Intentar usar Kiosk Handoff primero (estrategia inteligente)
      final handoffSuccess = await KioskHandoffService.instance.launchAppWithHandoff(packageName, appName);

      if (handoffSuccess) {
        print('✅ App lanzada con Kiosk Handoff: $appName');
        return true;
      } else {
        // Fallback al método tradicional
        print('🔄 Fallback al lanzamiento tradicional...');
        await LaunchApp.openApp(
          androidPackageName: packageName,
          iosUrlScheme: '', // No usado en Android
          appStoreLink: '', // No usado para apps instaladas
        );

        print('✅ App lanzada exitosamente: $appName');
        return true;
      }

    } catch (e) {
      print('❌ Error lanzando app $packageName: $e');
      return false;
    }
  }

  /// Apps por defecto para personas mayores
  List<AppItem> _getDefaultApps() {
    return [
      AppItem(
        id: 'com.android.dialer',
        name: 'Teléfono',
        packageName: 'com.android.dialer',
        category: AppCategory.getCategoryById('communication') ?? AppCategory.defaultCategories.first,
        icon: Icons.phone,
        color: AppColors.success,
      ),
      AppItem(
        id: 'com.android.mms',
        name: 'Mensajes',
        packageName: 'com.android.mms',
        category: AppCategory.getCategoryById('communication') ?? AppCategory.defaultCategories.first,
        icon: Icons.sms,
        color: AppColors.primary,
      ),
      AppItem(
        id: 'com.android.camera2',
        name: 'Cámara',
        packageName: 'com.android.camera2',
        category: AppCategory.getCategoryById('tools') ?? AppCategory.defaultCategories.first,
        icon: Icons.camera_alt,
        color: AppColors.info,
      ),
      AppItem(
        id: 'com.whatsapp',
        name: 'WhatsApp',
        packageName: 'com.whatsapp',
        category: AppCategory.getCategoryById('communication') ?? AppCategory.defaultCategories.first,
        icon: Icons.chat,
        color: AppColors.success,
      ),
      AppItem(
        id: 'com.google.android.youtube',
        name: 'YouTube',
        packageName: 'com.google.android.youtube',
        category: AppCategory.getCategoryById('entertainment') ?? AppCategory.defaultCategories.first,
        icon: Icons.play_circle_fill,
        color: AppColors.error,
      ),
      AppItem(
        id: 'com.google.android.gm',
        name: 'Gmail',
        packageName: 'com.google.android.gm',
        category: AppCategory.getCategoryById('communication') ?? AppCategory.defaultCategories.first,
        icon: Icons.mail,
        color: AppColors.error,
      ),
      AppItem(
        id: 'com.android.contacts',
        name: 'Contactos',
        packageName: 'com.android.contacts',
        category: AppCategory.getCategoryById('communication') ?? AppCategory.defaultCategories.first,
        icon: Icons.contacts,
        color: AppColors.accent,
      ),
      AppItem(
        id: 'com.android.settings',
        name: 'Configuración',
        packageName: 'com.android.settings',
        category: AppCategory.getCategoryById('settings') ?? AppCategory.defaultCategories.first,
        icon: Icons.settings,
        color: AppColors.textSecondary,
      ),
    ];
  }

  /// Obtener app por defecto por package name
  AppItem _getDefaultAppByPackage(String packageName) {
    final appData = _popularApps[packageName];
    if (appData != null) {
      return AppItem(
        id: packageName,
        name: appData.name,
        packageName: packageName,
        category: AppCategory.getCategoryForApp(packageName) ?? AppCategory.defaultCategories.last,
        icon: appData.icon,
        color: appData.color,
      );
    }

    return AppItem(
      id: packageName,
      name: 'App Desconocida',
      packageName: packageName,
      category: AppCategory.defaultCategories.last,
      icon: Icons.android,
      color: AppColors.textSecondary,
    );
  }
}

/// Datos de una aplicación popular
class AppData {
  final String name;
  final IconData icon;
  final Color color;
  
  const AppData(this.name, this.icon, this.color);
}
