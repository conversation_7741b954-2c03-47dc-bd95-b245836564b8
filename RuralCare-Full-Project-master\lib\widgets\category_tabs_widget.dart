import 'package:flutter/material.dart';
import '../models/app_category.dart';
import '../models/app_item.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';

/// Widget que muestra pestañas de categorías para organizar las aplicaciones
class CategoryTabsWidget extends StatefulWidget {
  final List<AppItem> apps;
  final Function(String categoryId) onCategoryChanged;
  final String selectedCategoryId;

  const CategoryTabsWidget({
    super.key,
    required this.apps,
    required this.onCategoryChanged,
    this.selectedCategoryId = 'favorites',
  });

  @override
  State<CategoryTabsWidget> createState() => _CategoryTabsWidgetState();
}

class _CategoryTabsWidgetState extends State<CategoryTabsWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<AppCategory> _categoriesWithApps;

  @override
  void initState() {
    super.initState();
    _updateCategories();
    _initializeTabController();
  }

  @override
  void didUpdateWidget(CategoryTabsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.apps != widget.apps) {
      _updateCategories();
      _updateTabController();
    }
  }

  void _updateCategories() {
    _categoriesWithApps = AppCategory.getCategoriesWithApps(widget.apps);
    
    // Asegurar que siempre tengamos al menos "Todas" como categoría
    if (_categoriesWithApps.isEmpty) {
      _categoriesWithApps = [
        const AppCategory(
          id: 'all',
          name: 'Todas',
          displayName: 'Todas las Apps',
          icon: Icons.apps,
          color: AppColors.primary,
          packageNames: [],
          description: 'Todas las aplicaciones',
        ),
      ];
    }
  }

  void _initializeTabController() {
    _tabController = TabController(
      length: _categoriesWithApps.length,
      vsync: this,
    );
    
    // Encontrar el índice de la categoría seleccionada
    final selectedIndex = _categoriesWithApps.indexWhere(
      (category) => category.id == widget.selectedCategoryId,
    );
    
    if (selectedIndex != -1) {
      _tabController.index = selectedIndex;
    }
    
    _tabController.addListener(_onTabChanged);
  }

  void _updateTabController() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _initializeTabController();
    setState(() {});
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;
    
    final selectedCategory = _categoriesWithApps[_tabController.index];
    widget.onCategoryChanged(selectedCategory.id);
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryDark.withOpacity(0.9),
            AppColors.surface.withOpacity(0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Pestañas de categorías
          Container(
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppDimensions.radiusM),
              ),
              gradient: LinearGradient(
                colors: [
                  AppColors.primary.withOpacity(0.8),
                  AppColors.accent.withOpacity(0.8),
                ],
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              indicatorColor: AppColors.textPrimary,
              indicatorWeight: 3,
              labelColor: AppColors.textPrimary,
              unselectedLabelColor: AppColors.textSecondary,
              labelStyle: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
              tabs: _categoriesWithApps.map((category) {
                final appCount = _getAppCountForCategory(category.id);
                return Tab(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          category.icon,
                          size: 20,
                          color: category.color,
                        ),
                        const SizedBox(width: 8),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              category.name,
                              style: const TextStyle(fontSize: 12),
                            ),
                            if (appCount > 0)
                              Text(
                                '$appCount apps',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppColors.textSecondary.withOpacity(0.8),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Descripción de la categoría seleccionada
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.surface.withOpacity(0.5),
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: _buildCategoryDescription(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDescription() {
    if (_categoriesWithApps.isEmpty) {
      return const SizedBox.shrink();
    }
    
    final selectedCategory = _categoriesWithApps[_tabController.index];
    final appCount = _getAppCountForCategory(selectedCategory.id);
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: selectedCategory.color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            selectedCategory.icon,
            color: selectedCategory.color,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                selectedCategory.displayName,
                style: const TextStyle(
                  color: AppColors.textPrimary,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '${selectedCategory.description} • $appCount aplicaciones',
                style: TextStyle(
                  color: AppColors.textSecondary.withOpacity(0.8),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  int _getAppCountForCategory(String categoryId) {
    if (categoryId == 'all') {
      return widget.apps.where((app) => !app.isHidden).length;
    }
    
    final filteredApps = AppCategory.filterAppsByCategory(widget.apps, categoryId);
    return filteredApps.where((app) => !app.isHidden).length;
  }
}
