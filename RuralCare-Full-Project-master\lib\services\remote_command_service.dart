import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'backend_service.dart';
import 'wallpaper_service.dart';
import 'device_control_service.dart';

/// Servicio para manejar comandos remotos del backend
class RemoteCommandService {
  static final RemoteCommandService _instance = RemoteCommandService._internal();
  factory RemoteCommandService() => _instance;
  RemoteCommandService._internal();

  final BackendService _backendService = BackendService();
  Timer? _commandTimer;
  bool _isProcessingCommands = false;

  // Callbacks para diferentes tipos de comandos
  Function(String message, bool flash)? onMessageReceived;
  Function(String wallpaperUrl)? onWallpaperChange;
  Function()? onSuspendDevice;
  Function()? onActivateDevice;
  Function()? onRebootDevice;
  Function()? onSyncRequest;

  /// Inicializar el servicio
  void initialize() {
    print('📡 RemoteCommandService inicializado');
    _startCommandPolling();
  }

  /// Iniciar polling de comandos
  void _startCommandPolling() {
    // Verificar comandos cada 30 segundos
    _commandTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _checkForCommands();
    });
    
    // Verificar comandos inmediatamente
    _checkForCommands();
  }

  /// Verificar comandos pendientes
  Future<void> _checkForCommands() async {
    if (_isProcessingCommands) return;
    
    try {
      _isProcessingCommands = true;
      
      // Verificar conectividad
      final isConnected = await _backendService.checkConnectivity();
      if (!isConnected) {
        print('📡 Sin conexión con backend, saltando verificación de comandos');
        return;
      }

      // Obtener comandos pendientes
      final commands = await _backendService.getCommands();
      
      if (commands.isNotEmpty) {
        print('📨 ${commands.length} comandos pendientes recibidos');
        
        for (final command in commands) {
          await _processCommand(command);
        }
      }
      
    } catch (e) {
      print('❌ Error verificando comandos: $e');
    } finally {
      _isProcessingCommands = false;
    }
  }

  /// Procesar un comando individual
  Future<void> _processCommand(Map<String, dynamic> command) async {
    try {
      final commandId = command['id'];
      final commandType = command['command_type'];
      final payload = command['payload'] != null 
          ? (command['payload'] is String 
              ? jsonDecode(command['payload']) 
              : command['payload'])
          : <String, dynamic>{};

      print('⚡ Procesando comando: $commandType');

      bool success = false;
      String? errorMessage;

      switch (commandType) {
        case 'message':
          success = await _handleMessageCommand(payload);
          break;
          
        case 'wallpaper':
          success = await _handleWallpaperCommand(payload);
          break;
          
        case 'suspend':
          success = await _handleSuspendCommand();
          break;
          
        case 'activate':
          success = await _handleActivateCommand();
          break;
          
        case 'reboot':
          success = await _handleRebootCommand();
          break;
          
        case 'sync':
          success = await _handleSyncCommand();
          break;
          
        default:
          errorMessage = 'Tipo de comando no soportado: $commandType';
          print('❌ $errorMessage');
      }

      // Confirmar ejecución del comando
      final status = success ? 'delivered' : 'failed';
      await _backendService.acknowledgeCommand(commandId, status, errorMessage: errorMessage);
      
      print(success ? '✅ Comando $commandType ejecutado' : '❌ Comando $commandType falló');
      
    } catch (e) {
      print('❌ Error procesando comando: $e');
      
      // Confirmar como fallido
      final commandId = command['id'];
      if (commandId != null) {
        await _backendService.acknowledgeCommand(commandId, 'failed', errorMessage: e.toString());
      }
    }
  }

  /// Manejar comando de mensaje
  Future<bool> _handleMessageCommand(Map<String, dynamic> payload) async {
    try {
      final message = payload['message'] ?? 'Mensaje del administrador';
      final flash = payload['flash'] ?? false;
      
      if (onMessageReceived != null) {
        onMessageReceived!(message, flash);
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ Error manejando comando de mensaje: $e');
      return false;
    }
  }

  /// Manejar comando de cambio de fondo
  Future<bool> _handleWallpaperCommand(Map<String, dynamic> payload) async {
    try {
      final wallpaperUrl = payload['wallpaper_url'] ?? '';

      if (wallpaperUrl.isNotEmpty) {
        // Usar el servicio nativo para cambiar el wallpaper
        final wallpaperService = WallpaperService();
        final success = await wallpaperService.setWallpaperFromUrl(wallpaperUrl);

        // También notificar al callback si existe
        if (onWallpaperChange != null) {
          onWallpaperChange!(wallpaperUrl);
        }

        return success;
      }

      return false;
    } catch (e) {
      print('❌ Error manejando comando de wallpaper: $e');
      return false;
    }
  }

  /// Manejar comando de suspensión
  Future<bool> _handleSuspendCommand() async {
    try {
      // Guardar estado de suspensión
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('device_suspended', true);
      
      if (onSuspendDevice != null) {
        onSuspendDevice!();
      }
      
      return true;
    } catch (e) {
      print('❌ Error manejando comando de suspensión: $e');
      return false;
    }
  }

  /// Manejar comando de activación
  Future<bool> _handleActivateCommand() async {
    try {
      // Quitar estado de suspensión
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('device_suspended', false);
      
      if (onActivateDevice != null) {
        onActivateDevice!();
      }
      
      return true;
    } catch (e) {
      print('❌ Error manejando comando de activación: $e');
      return false;
    }
  }

  /// Manejar comando de reinicio
  Future<bool> _handleRebootCommand() async {
    try {
      // Usar el servicio nativo para reiniciar el dispositivo
      final deviceControlService = DeviceControlService();
      final success = await deviceControlService.rebootDevice(delaySeconds: 10);

      // También notificar al callback si existe
      if (onRebootDevice != null) {
        onRebootDevice!();
      }

      return success;
    } catch (e) {
      print('❌ Error manejando comando de reinicio: $e');
      return false;
    }
  }

  /// Manejar comando de sincronización
  Future<bool> _handleSyncCommand() async {
    try {
      if (onSyncRequest != null) {
        onSyncRequest!();
      }
      
      // Forzar registro del dispositivo
      await _backendService.registerDevice();
      
      return true;
    } catch (e) {
      print('❌ Error manejando comando de sync: $e');
      return false;
    }
  }

  /// Verificar si el dispositivo está suspendido
  Future<bool> isDeviceSuspended() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('device_suspended') ?? false;
    } catch (e) {
      print('❌ Error verificando estado de suspensión: $e');
      return false;
    }
  }

  /// Forzar verificación de comandos
  Future<void> forceCheckCommands() async {
    await _checkForCommands();
  }

  /// Detener el servicio
  void dispose() {
    _commandTimer?.cancel();
    _commandTimer = null;
    print('📡 RemoteCommandService detenido');
  }
}
