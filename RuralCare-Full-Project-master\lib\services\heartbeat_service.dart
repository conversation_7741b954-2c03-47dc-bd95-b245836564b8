import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'backend_service.dart';

/// Servicio para mantener conexión con el backend mediante heartbeats
class HeartbeatService {
  static final HeartbeatService _instance = HeartbeatService._internal();
  factory HeartbeatService() => _instance;
  HeartbeatService._internal();

  final BackendService _backendService = BackendService();
  Timer? _heartbeatTimer;
  Timer? _registrationTimer;
  
  bool _isConnected = false;
  bool _isRegistered = false;
  DateTime? _lastSuccessfulHeartbeat;
  int _failedHeartbeats = 0;
  
  // Configuración
  static const Duration _heartbeatInterval = Duration(minutes: 2);
  static const Duration _registrationInterval = Duration(minutes: 10);
  static const int _maxFailedHeartbeats = 3;

  // Callbacks
  Function(bool isConnected)? onConnectionStatusChanged;
  Function(bool isRegistered)? onRegistrationStatusChanged;

  /// Inicializar el servicio
  Future<void> initialize() async {
    print('💓 HeartbeatService inicializando...');
    
    // Intentar registro inicial
    await _attemptRegistration();
    
    // Iniciar timers
    _startHeartbeatTimer();
    _startRegistrationTimer();
    
    print('💓 HeartbeatService inicializado');
  }

  /// Iniciar timer de heartbeat
  void _startHeartbeatTimer() {
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      _sendHeartbeat();
    });
    
    // Enviar heartbeat inmediatamente
    _sendHeartbeat();
  }

  /// Iniciar timer de registro
  void _startRegistrationTimer() {
    _registrationTimer = Timer.periodic(_registrationInterval, (timer) {
      if (!_isRegistered) {
        _attemptRegistration();
      }
    });
  }

  /// Intentar registro del dispositivo
  Future<void> _attemptRegistration() async {
    try {
      print('📱 Intentando registro del dispositivo...');
      
      final success = await _backendService.registerDevice();
      
      if (success != _isRegistered) {
        _isRegistered = success;
        onRegistrationStatusChanged?.call(_isRegistered);
        
        if (_isRegistered) {
          print('✅ Dispositivo registrado exitosamente');
          await _saveRegistrationStatus(true);
        } else {
          print('❌ Fallo en registro del dispositivo');
          await _saveRegistrationStatus(false);
        }
      }
      
    } catch (e) {
      print('❌ Error en registro del dispositivo: $e');
      _isRegistered = false;
      onRegistrationStatusChanged?.call(_isRegistered);
      await _saveRegistrationStatus(false);
    }
  }

  /// Enviar heartbeat
  Future<void> _sendHeartbeat() async {
    try {
      // Solo enviar heartbeat si estamos registrados
      if (!_isRegistered) {
        print('⚠️ Dispositivo no registrado, saltando heartbeat');
        return;
      }

      final success = await _backendService.sendHeartbeat();
      
      if (success) {
        _lastSuccessfulHeartbeat = DateTime.now();
        _failedHeartbeats = 0;
        
        if (!_isConnected) {
          _isConnected = true;
          onConnectionStatusChanged?.call(_isConnected);
          print('🟢 Conexión con backend establecida');
        }
        
        await _saveConnectionStatus(true);
        
      } else {
        _failedHeartbeats++;
        print('❌ Heartbeat falló (${_failedHeartbeats}/$_maxFailedHeartbeats)');
        
        if (_failedHeartbeats >= _maxFailedHeartbeats && _isConnected) {
          _isConnected = false;
          onConnectionStatusChanged?.call(_isConnected);
          print('🔴 Conexión con backend perdida');
          await _saveConnectionStatus(false);
        }
      }
      
    } catch (e) {
      print('❌ Error enviando heartbeat: $e');
      _failedHeartbeats++;
      
      if (_failedHeartbeats >= _maxFailedHeartbeats && _isConnected) {
        _isConnected = false;
        onConnectionStatusChanged?.call(_isConnected);
        await _saveConnectionStatus(false);
      }
    }
  }

  /// Guardar estado de conexión
  Future<void> _saveConnectionStatus(bool isConnected) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('backend_connected', isConnected);
      await prefs.setString('last_heartbeat', DateTime.now().toIso8601String());
    } catch (e) {
      print('❌ Error guardando estado de conexión: $e');
    }
  }

  /// Guardar estado de registro
  Future<void> _saveRegistrationStatus(bool isRegistered) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('device_registered', isRegistered);
      if (isRegistered) {
        await prefs.setString('registration_date', DateTime.now().toIso8601String());
      }
    } catch (e) {
      print('❌ Error guardando estado de registro: $e');
    }
  }

  /// Cargar estado guardado
  Future<void> loadSavedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _isConnected = prefs.getBool('backend_connected') ?? false;
      _isRegistered = prefs.getBool('device_registered') ?? false;
      
      final lastHeartbeatStr = prefs.getString('last_heartbeat');
      if (lastHeartbeatStr != null) {
        _lastSuccessfulHeartbeat = DateTime.parse(lastHeartbeatStr);
      }
      
      print('📊 Estado cargado - Conectado: $_isConnected, Registrado: $_isRegistered');
      
    } catch (e) {
      print('❌ Error cargando estado guardado: $e');
    }
  }

  /// Forzar heartbeat inmediato
  Future<void> forceHeartbeat() async {
    await _sendHeartbeat();
  }

  /// Forzar registro inmediato
  Future<void> forceRegistration() async {
    await _attemptRegistration();
  }

  /// Obtener estadísticas de conexión
  Map<String, dynamic> getConnectionStats() {
    return {
      'isConnected': _isConnected,
      'isRegistered': _isRegistered,
      'lastSuccessfulHeartbeat': _lastSuccessfulHeartbeat?.toIso8601String(),
      'failedHeartbeats': _failedHeartbeats,
      'deviceId': _backendService.deviceId,
    };
  }

  /// Verificar si la conexión está activa
  bool get isConnected => _isConnected;

  /// Verificar si el dispositivo está registrado
  bool get isRegistered => _isRegistered;

  /// Obtener último heartbeat exitoso
  DateTime? get lastSuccessfulHeartbeat => _lastSuccessfulHeartbeat;

  /// Obtener número de heartbeats fallidos
  int get failedHeartbeats => _failedHeartbeats;

  /// Reiniciar contadores
  void resetCounters() {
    _failedHeartbeats = 0;
    _isConnected = false;
    _isRegistered = false;
    _lastSuccessfulHeartbeat = null;
  }

  /// Detener el servicio
  void dispose() {
    _heartbeatTimer?.cancel();
    _registrationTimer?.cancel();
    _heartbeatTimer = null;
    _registrationTimer = null;
    print('💓 HeartbeatService detenido');
  }
}
