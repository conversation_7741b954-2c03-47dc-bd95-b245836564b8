-- 📱 RuralCare Backend - Base de Datos MySQL
-- Optimizado para BanaHosting

-- Crear base de datos (ejecutar solo si tienes permisos)
-- CREATE DATABASE IF NOT EXISTS ruralcare CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE ruralcare;

-- 📱 Tabla de dispositivos
CREATE TABLE IF NOT EXISTS devices (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    device_id VARCHAR(255) UNIQUE NOT NULL,
    device_name VARCHAR(255),
    launcher_version VARCHAR(50),
    android_version VARCHAR(50),
    model VARCHAR(255),
    manufacturer VARCHAR(255),
    brand VARCHAR(255),
    sdk_int INT,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    status ENUM('active', 'suspended', 'offline') DEFAULT 'active',
    device_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 📡 Tabla de comandos remotos
CREATE TABLE IF NOT EXISTS remote_commands (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    device_id CHAR(36) NOT NULL,
    command_type ENUM('sync', 'message', 'suspend', 'activate', 'wallpaper', 'reboot') NOT NULL,
    payload JSON,
    status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMP NULL,
    error_message TEXT,
    
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 📊 Tabla de logs de dispositivos
CREATE TABLE IF NOT EXISTS device_logs (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    device_id CHAR(36) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    INDEX idx_device_id (device_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_event_type (event_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 👥 Tabla de usuarios administradores
CREATE TABLE IF NOT EXISTS admin_users (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'viewer') DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 📈 Tabla de estadísticas (reemplaza Redis)
CREATE TABLE IF NOT EXISTS device_statistics (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    device_id CHAR(36) NOT NULL,
    stat_date DATE DEFAULT (CURRENT_DATE),
    heartbeats_count INT DEFAULT 0,
    commands_received INT DEFAULT 0,
    commands_executed INT DEFAULT 0,
    uptime_minutes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE,
    UNIQUE KEY unique_device_date (device_id, stat_date),
    INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 🔐 Tabla de sesiones (reemplaza Redis)
CREATE TABLE IF NOT EXISTS user_sessions (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 🚦 Tabla de rate limiting (reemplaza Redis)
CREATE TABLE IF NOT EXISTS rate_limits (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    ip_address VARCHAR(45) NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    requests_count INT DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_ip_endpoint (ip_address, endpoint),
    INDEX idx_window_start (window_start)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 👤 Insertar usuario administrador por defecto
-- Contraseña: admin123 (cambiar en producción)
INSERT IGNORE INTO admin_users (username, email, password_hash, role) 
VALUES (
    'admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'admin'
);

-- Procedimiento para limpiar datos antiguos se creará después

-- ✅ Mensaje de confirmación
SELECT 'Base de datos inicializada correctamente para BanaHosting' as message;
