import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_strings.dart';
import '../constants/app_theme.dart';
import '../services/kiosk_mode_service.dart';

/// Pantalla de administrador para configurar el modo kiosko y otras opciones avanzadas
class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  bool _isDeviceAdminEnabled = false;
  bool _isKioskModeActive = false;
  bool _isDefaultLauncher = false;
  bool _isDeviceOwner = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkCurrentStatus();
  }

  Future<void> _checkCurrentStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final deviceAdminEnabled = await KioskModeService.instance.isDeviceAdminEnabled();
      final kioskModeActive = await KioskModeService.instance.isKioskModeActive();
      final defaultLauncher = await KioskModeService.instance.isDefaultLauncher();
      final deviceOwner = await KioskModeService.instance.isDeviceOwner();

      setState(() {
        _isDeviceAdminEnabled = deviceAdminEnabled;
        _isKioskModeActive = kioskModeActive;
        _isDefaultLauncher = defaultLauncher;
        _isDeviceOwner = deviceOwner;
      });
    } catch (e) {
      _showErrorMessage('Error verificando estado: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.adminTheme,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(AppStrings.adminMode),
          backgroundColor: AppColors.adminPrimary,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _checkCurrentStatus,
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildAdminContent(),
      ),
    );
  }

  Widget _buildAdminContent() {
    return Container(
      decoration: const BoxDecoration(
        gradient: AppColors.adminGradient,
      ),
      child: ListView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        children: [
          _buildStatusCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildLauncherConfigCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildKioskModeCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildSystemConfigCard(),
          const SizedBox(height: AppDimensions.paddingL),
          if (!_isDeviceOwner) _buildDeviceOwnerInstructionsCard(),
          if (!_isDeviceOwner) const SizedBox(height: AppDimensions.paddingL),
          _buildExitButton(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Estado del Sistema',
              style: TextStyle(
                fontSize: AppDimensions.fontL,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            _buildStatusItem(
              'Administrador de Dispositivo',
              _isDeviceAdminEnabled,
              Icons.admin_panel_settings,
            ),
            _buildStatusItem(
              'Device Owner (Permisos Completos)',
              _isDeviceOwner,
              Icons.security,
            ),
            _buildStatusItem(
              'Launcher por Defecto',
              _isDefaultLauncher,
              Icons.home,
            ),
            _buildStatusItem(
              'Modo Kiosko Activo',
              _isKioskModeActive,
              Icons.lock,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String title, bool status, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        children: [
          Icon(
            icon,
            color: status ? AppColors.success : AppColors.error,
            size: AppDimensions.iconM,
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: AppDimensions.fontM),
            ),
          ),
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? AppColors.success : AppColors.error,
            size: AppDimensions.iconM,
          ),
        ],
      ),
    );
  }

  Widget _buildLauncherConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuración de Launcher',
              style: TextStyle(
                fontSize: AppDimensions.fontL,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            ElevatedButton.icon(
              onPressed: _setAsDefaultLauncher,
              icon: const Icon(Icons.home),
              label: const Text('Establecer como Launcher por Defecto'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.adminPrimary,
                minimumSize: const Size(double.infinity, 50),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKioskModeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Modo Kiosko',
              style: TextStyle(
                fontSize: AppDimensions.fontL,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'El modo kiosko bloquea la navegación del sistema para prevenir salidas involuntarias.',
              style: TextStyle(fontSize: AppDimensions.fontS),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            if (!_isDeviceAdminEnabled)
              ElevatedButton.icon(
                onPressed: _requestDeviceAdminPermission,
                icon: const Icon(Icons.admin_panel_settings),
                label: const Text('Habilitar Permisos de Administrador'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.warning,
                  minimumSize: const Size(double.infinity, 50),
                ),
              ),
            if (_isDeviceAdminEnabled && !_isKioskModeActive)
              ElevatedButton.icon(
                onPressed: _enableKioskMode,
                icon: const Icon(Icons.lock),
                label: const Text('Activar Modo Kiosko'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  minimumSize: const Size(double.infinity, 50),
                ),
              ),
            if (_isKioskModeActive)
              ElevatedButton.icon(
                onPressed: _disableKioskMode,
                icon: const Icon(Icons.lock_open),
                label: const Text('Desactivar Modo Kiosko'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                  minimumSize: const Size(double.infinity, 50),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuración del Sistema',
              style: TextStyle(
                fontSize: AppDimensions.fontL,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            ElevatedButton.icon(
              onPressed: _setupCompleteKioskMode,
              icon: const Icon(Icons.security),
              label: const Text('Configuración Completa de Kiosko'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.adminPrimary,
                minimumSize: const Size(double.infinity, 50),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            ElevatedButton.icon(
              onPressed: _showAllowedApps,
              icon: const Icon(Icons.list),
              label: const Text('Ver Apps Permitidas'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.info,
                minimumSize: const Size(double.infinity, 50),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceOwnerInstructionsCard() {
    return Card(
      color: AppColors.warning.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: AppColors.warning),
                const SizedBox(width: AppDimensions.paddingS),
                const Text(
                  'Lock Task Mode Inteligente',
                  style: TextStyle(
                    fontSize: AppDimensions.fontL,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'Para que las apps (Cámara, Teléfono, etc.) se abran normalmente en modo kiosko, necesitas configurar la app como Device Owner usando ADB:',
              style: TextStyle(fontSize: AppDimensions.fontS),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.adminBackground,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: const Text(
                'adb shell dpm set-device-owner com.ruralcare.ruralcare/.RuralCareDeviceAdminReceiver',
                style: TextStyle(
                  fontSize: 10,
                  fontFamily: 'monospace',
                  color: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              '⚠️ Sin Device Owner: Solo modo kiosko básico (requiere "unpin" para usar otras apps)\n✅ Con Device Owner: Apps permitidas se abren normalmente',
              style: TextStyle(fontSize: AppDimensions.fontXS),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExitButton() {
    return ElevatedButton.icon(
      onPressed: () => Navigator.of(context).pop(),
      icon: const Icon(Icons.exit_to_app),
      label: const Text(AppStrings.exitAdmin),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.textSecondary,
        minimumSize: const Size(double.infinity, 60),
      ),
    );
  }

  Future<void> _setAsDefaultLauncher() async {
    try {
      await KioskModeService.instance.setAsDefaultLauncher();
      _showSuccessMessage('Configuración de launcher abierta');
      await _checkCurrentStatus();
    } catch (e) {
      _showErrorMessage('Error configurando launcher: $e');
    }
  }

  Future<void> _requestDeviceAdminPermission() async {
    try {
      await KioskModeService.instance.requestDeviceAdminPermission();
      _showSuccessMessage('Solicitud de permisos enviada');
      // Esperar un poco y verificar el estado
      await Future.delayed(const Duration(seconds: 2));
      await _checkCurrentStatus();
    } catch (e) {
      _showErrorMessage('Error solicitando permisos: $e');
    }
  }

  Future<void> _enableKioskMode() async {
    try {
      final success = await KioskModeService.instance.enableKioskMode();
      if (success) {
        _showSuccessMessage('Modo kiosko activado');
        await _checkCurrentStatus();
      } else {
        _showErrorMessage('No se pudo activar el modo kiosko');
      }
    } catch (e) {
      _showErrorMessage('Error activando modo kiosko: $e');
    }
  }

  Future<void> _disableKioskMode() async {
    try {
      final success = await KioskModeService.instance.disableKioskMode();
      if (success) {
        _showSuccessMessage('Modo kiosko desactivado');
        await _checkCurrentStatus();
      } else {
        _showErrorMessage('No se pudo desactivar el modo kiosko');
      }
    } catch (e) {
      _showErrorMessage('Error desactivando modo kiosko: $e');
    }
  }

  Future<void> _setupCompleteKioskMode() async {
    try {
      final success = await KioskModeService.instance.setupKioskMode();
      if (success) {
        _showSuccessMessage('Configuración completa de kiosko aplicada');
        await _checkCurrentStatus();
      } else {
        _showErrorMessage('No se pudo completar la configuración de kiosko');
      }
    } catch (e) {
      _showErrorMessage('Error en configuración completa: $e');
    }
  }

  Future<void> _showAllowedApps() async {
    try {
      final allowedApps = await KioskModeService.instance.getAllowedApps();

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Apps Permitidas en Modo Kiosko'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: allowedApps.isEmpty
                ? const Center(
                    child: Text('No hay apps configuradas en la lista blanca'),
                  )
                : ListView.builder(
                    itemCount: allowedApps.length,
                    itemBuilder: (context, index) {
                      final packageName = allowedApps[index];
                      return ListTile(
                        leading: const Icon(Icons.check_circle, color: AppColors.success),
                        title: Text(
                          packageName,
                          style: const TextStyle(fontSize: 12),
                        ),
                        subtitle: Text(
                          _getAppDisplayName(packageName),
                          style: const TextStyle(fontSize: 10),
                        ),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cerrar'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorMessage('Error obteniendo apps permitidas: $e');
    }
  }

  String _getAppDisplayName(String packageName) {
    switch (packageName) {
      case 'com.ruralcare.ruralcare':
        return 'RuralCare';
      case 'com.android.dialer':
      case 'com.google.android.dialer':
      case 'com.samsung.android.dialer':
        return 'Teléfono';
      case 'com.android.camera2':
      case 'com.android.camera':
      case 'com.google.android.GoogleCamera':
        return 'Cámara';
      case 'com.android.contacts':
      case 'com.google.android.contacts':
        return 'Contactos';
      case 'com.android.mms':
      case 'com.google.android.apps.messaging':
        return 'Mensajes';
      case 'com.android.emergency':
        return 'Emergencia';
      case 'com.google.android.youtube':
        return 'YouTube';
      case 'com.android.settings':
        return 'Configuraciones';
      default:
        return 'App del Sistema';
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
