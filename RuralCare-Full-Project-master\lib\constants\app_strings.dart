/// Textos y cadenas de la aplicación
/// Centralizados para facilitar la localización futura
class AppStrings {
  // Información general de la app
  static const String appName = 'RuralCare';
  static const String appDescription = 'Launcher personalizado para personas mayores en zonas rurales';
  
  // Pantalla principal
  static const String welcome = 'Bienvenido';
  static const String goodMorning = 'Buenos días';
  static const String goodAfternoon = 'Buenas tardes';
  static const String goodEvening = 'Buenas noches';
  
  // Navegación principal
  static const String home = 'Inicio';
  static const String apps = 'Aplicaciones';
  static const String settings = 'Configuración';
  static const String help = 'Ayuda';
  
  // Aplicaciones comunes
  static const String phone = 'Teléfono';
  static const String messages = 'Mensajes';
  static const String camera = 'Cámara';
  static const String gallery = 'Galería';
  static const String internet = 'Internet';
  static const String email = 'Correo';
  static const String calendar = 'Calendario';
  static const String contacts = 'Contactos';
  static const String music = 'Música';
  static const String videos = 'Videos';
  static const String weather = 'Clima';
  static const String youtube = 'Youtube';
  static const String news = 'Noticias';
  
  // Configuraciones básicas
  static const String volume = 'Volumen';
  static const String brightness = 'Brillo';
  static const String fontSize = 'Tamaño de letra';
  static const String wifi = 'WiFi';
  static const String bluetooth = 'Bluetooth';
  static const String airplane = 'Modo avión';
  
  // Controles de sistema
  static const String increaseVolume = 'Subir volumen';
  static const String decreaseVolume = 'Bajar volumen';
  static const String increaseBrightness = 'Subir brillo';
  static const String decreaseBrightness = 'Bajar brillo';
  static const String increaseFontSize = 'Aumentar letra';
  static const String decreaseFontSize = 'Reducir letra';
  
  // Estados de conexión
  static const String connected = 'Conectado';
  static const String disconnected = 'Desconectado';
  static const String connecting = 'Conectando...';
  static const String noInternet = 'Sin internet';
  static const String wifiConnected = 'WiFi conectado';
  static const String wifiDisconnected = 'WiFi desconectado';
  
  // Modo administrador
  static const String adminMode = 'Modo Administrador';
  static const String enterPin = 'Ingrese PIN';
  static const String wrongPin = 'PIN incorrecto';
  static const String adminSettings = 'Configuración Avanzada';
  static const String exitAdmin = 'Salir del modo administrador';
  static const String changeWallpaper = 'Cambiar fondo de pantalla';
  static const String systemInfo = 'Información del sistema';
  static const String syncServer = 'Sincronizar con servidor';
  static const String deviceStatus = 'Estado del dispositivo';
  
  // Mensajes de ayuda
  static const String helpTitle = 'Ayuda y Soporte';
  static const String helpDescription = 'Aquí encontrará información útil para usar la aplicación';
  static const String howToUse = '¿Cómo usar esta aplicación?';
  static const String contactSupport = 'Contactar soporte';
  static const String emergencyContact = 'Contacto de emergencia';
  
  // Mensajes de error
  static const String error = 'Error';
  static const String errorGeneral = 'Ha ocurrido un error inesperado';
  static const String errorNetwork = 'Error de conexión a internet';
  static const String errorPermission = 'Permisos insuficientes';
  static const String errorAppNotFound = 'Aplicación no encontrada';
  static const String errorInstallApp = 'Error al instalar aplicación';
  
  // Mensajes de confirmación
  static const String confirm = 'Confirmar';
  static const String cancel = 'Cancelar';
  static const String accept = 'Aceptar';
  static const String ok = 'OK';
  static const String yes = 'Sí';
  static const String no = 'No';
  static const String save = 'Guardar';
  static const String delete = 'Eliminar';
  static const String edit = 'Editar';
  
  // Mensajes de estado
  static const String loading = 'Cargando...';
  static const String saving = 'Guardando...';
  static const String syncing = 'Sincronizando...';
  static const String updating = 'Actualizando...';
  static const String installing = 'Instalando...';
  static const String uninstalling = 'Desinstalando...';
  
  // Accesibilidad
  static const String accessibilityButton = 'Botón';
  static const String accessibilityIcon = 'Icono';
  static const String accessibilityImage = 'Imagen';
  static const String accessibilityMenu = 'Menú';
  static const String accessibilityBack = 'Volver';
  static const String accessibilityClose = 'Cerrar';
  static const String accessibilityOpen = 'Abrir';
  
  // Tiempo y fecha
  static const String today = 'Hoy';
  static const String yesterday = 'Ayer';
  static const String tomorrow = 'Mañana';
  static const String morning = 'Mañana';
  static const String afternoon = 'Tarde';
  static const String evening = 'Noche';
  
  // Batería y sistema
  static const String battery = 'Batería';
  static const String batteryLow = 'Batería baja';
  static const String batteryCharging = 'Cargando';
  static const String storage = 'Almacenamiento';
  static const String memory = 'Memoria';
  static const String performance = 'Rendimiento';
  
  // Instrucciones simples
  static const String tapToOpen = 'Toque para abrir';
  static const String tapToSelect = 'Toque para seleccionar';
  static const String holdToOptions = 'Mantenga presionado para opciones';
  static const String swipeLeft = 'Deslice hacia la izquierda';
  static const String swipeRight = 'Deslice hacia la derecha';
  static const String swipeUp = 'Deslice hacia arriba';
  static const String swipeDown = 'Deslice hacia abajo';
}
