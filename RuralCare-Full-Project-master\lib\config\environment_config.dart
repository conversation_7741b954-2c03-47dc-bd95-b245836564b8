/// Configuración de entornos para la aplicación
class EnvironmentConfig {
  // Tipo de entorno
  static const String _environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  
  // Configuración por entorno
  static bool get isDevelopment => _environment == 'development';
  static bool get isProduction => _environment == 'production';
  static bool get isTesting => _environment == 'testing';
  
  // URLs del backend según entorno
  static String get backendUrl {
    switch (_environment) {
      case 'production':
        return 'https://your-domain.com/api';
      case 'testing':
        return 'http://test-server:3000/api';
      case 'development':
      default:
        // Detectar automáticamente si es emulador o dispositivo físico
        return _getDevelopmentUrl();
    }
  }

  /// Obtener URL de desarrollo según el tipo de dispositivo
  static String _getDevelopmentUrl() {
    // CONFIGURACIÓN PARA ENTREGA:
    // Cambiar esta URL según el entorno de despliegue

    // Para demostración local:
    return 'http://*************:3000/api';

    // Para producción (descomentar cuando esté listo):
    // return 'https://tu-dominio-produccion.com/api';

    // Para servidor de pruebas:
    // return 'http://servidor-pruebas:3000/api';
  }
  
  // Configuración de logs
  static bool get enableDebugLogs => isDevelopment || isTesting;
  static bool get enableCrashReporting => isProduction;
  static bool get enableAnalytics => isProduction;
  
  // Configuración de timeouts
  static Duration get connectionTimeout {
    return isProduction 
        ? const Duration(seconds: 15) 
        : const Duration(seconds: 30);
  }
  
  static Duration get heartbeatInterval {
    return isProduction 
        ? const Duration(minutes: 1) 
        : const Duration(minutes: 2);
  }
  
  static Duration get commandCheckInterval {
    return isProduction 
        ? const Duration(seconds: 15) 
        : const Duration(seconds: 30);
  }
  
  // Configuración de reintentos
  static int get maxRetries => isProduction ? 5 : 3;
  static Duration get retryDelay => const Duration(seconds: 5);
  
  // Configuración de base de datos local
  static String get databaseName => 'ruralcare_${_environment}.db';
  
  // Configuración de cache
  static Duration get cacheExpiration {
    return isProduction 
        ? const Duration(hours: 1) 
        : const Duration(minutes: 30);
  }
  
  // Configuración de seguridad
  static bool get enableSSLPinning => isProduction;
  static bool get requireHttps => isProduction;
  
  // Información del entorno actual
  static Map<String, dynamic> get environmentInfo => {
    'environment': _environment,
    'isDevelopment': isDevelopment,
    'isProduction': isProduction,
    'isTesting': isTesting,
    'backendUrl': backendUrl,
    'enableDebugLogs': enableDebugLogs,
    'connectionTimeout': connectionTimeout.inSeconds,
    'heartbeatInterval': heartbeatInterval.inMinutes,
    'commandCheckInterval': commandCheckInterval.inSeconds,
  };
  
  // Método para imprimir configuración (solo en desarrollo)
  static void printConfig() {
    if (!enableDebugLogs) return;
    
    print('🔧 Configuración del entorno:');
    environmentInfo.forEach((key, value) {
      print('   $key: $value');
    });
  }
}
