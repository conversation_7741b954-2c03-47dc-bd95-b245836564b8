const mysql = require('mysql2/promise');

async function testSuspendActivate() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'senior_launcher'
    });
    
    console.log('🧪 Probando secuencia suspender → activar...');
    
    // 1. Crear comando de suspensión
    console.log('1️⃣ Creando comando de suspensión...');
    const [suspendResult] = await connection.execute(`
      INSERT INTO remote_commands (device_id, command_type, payload, status, created_at)
      VALUES (?, ?, ?, ?, ?)
    `, [
      '65cfba18-6709-11f0-ac7a-58112232136a',
      'suspend',
      JSON.stringify({
        reason: 'Prueba de suspensión',
        timestamp: new Date().toISOString()
      }),
      'pending',
      new Date()
    ]);
    
    console.log(`✅ Comando de suspensión creado con ID: ${suspendResult.insertId}`);
    console.log('⏳ Esperando 35 segundos para que el launcher reciba el comando...');
    
    // Esperar 35 segundos
    await new Promise(resolve => setTimeout(resolve, 35000));
    
    // 2. Crear comando de activación
    console.log('2️⃣ Creando comando de activación...');
    const [activateResult] = await connection.execute(`
      INSERT INTO remote_commands (device_id, command_type, payload, status, created_at)
      VALUES (?, ?, ?, ?, ?)
    `, [
      '65cfba18-6709-11f0-ac7a-58112232136a',
      'activate',
      JSON.stringify({
        reason: 'Prueba de activación',
        timestamp: new Date().toISOString()
      }),
      'pending',
      new Date()
    ]);
    
    console.log(`✅ Comando de activación creado con ID: ${activateResult.insertId}`);
    console.log('⏳ El launcher debería activarse en máximo 30 segundos...');
    
    await connection.end();
    
    console.log('\n🎯 Secuencia de prueba completada:');
    console.log('1. El launcher debería mostrar pantalla roja de suspensión');
    console.log('2. Después de 35 segundos, debería regresar al launcher normal');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testSuspendActivate();
