import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'constants/app_theme.dart';
import 'constants/app_strings.dart';
import 'widgets/launcher_wrapper.dart';
import 'services/theme_persistence_service.dart';
import 'services/kiosk_handoff_service.dart';
import 'services/system_settings_service.dart';
import 'services/app_management_service.dart';
import 'services/backend_service.dart';
import 'services/heartbeat_service.dart';
import 'services/remote_command_service.dart';
import 'services/lock_screen_config_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar el tema navy elegante
  await ThemePersistenceService.instance.initializeTheme();

  // Inicializar el sistema de Kiosk Handoff
  await KioskHandoffService.instance.initializeHandoffSystem();

  // Inicializar configuraciones del sistema
  await SystemSettingsService.instance.initialize();

  // Inicializar gestión de aplicaciones
  await AppManagementService.instance.initialize();

  // Inicializar configuración de pantalla de bloqueo
  await LockScreenConfigService.instance.initialize();

  // Inicializar servicios de backend
  try {
    print('🔌 Inicializando servicios de backend...');

    // Inicializar servicio de backend
    await BackendService().initialize();

    // Inicializar servicio de heartbeat
    await HeartbeatService().initialize();

    // Inicializar servicio de comandos remotos
    RemoteCommandService().initialize();

    print('✅ Servicios de backend inicializados');
  } catch (e) {
    print('⚠️ Error inicializando servicios de backend: $e');
    print('📱 Continuando sin conectividad remota');
  }

  runApp(const RuralCareApp());
}

class RuralCareApp extends StatelessWidget {
  const RuralCareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppStrings.appName,
      theme: AppTheme.darkTheme, // Usar tema oscuro navy
      home: const LauncherWrapper(),
      debugShowCheckedModeBanner: false,

      // Configuración de accesibilidad
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            // Asegurar que el texto no se escale demasiado
            textScaler: TextScaler.linear(
              MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.5),
            ),
          ),
          child: child!,
        );
      },
    );
  }
}


