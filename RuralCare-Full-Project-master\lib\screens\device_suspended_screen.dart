import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../services/remote_command_service.dart';
import '../services/heartbeat_service.dart';

/// Pantalla que se muestra cuando el dispositivo está suspendido
class DeviceSuspendedScreen extends StatefulWidget {
  const DeviceSuspendedScreen({Key? key}) : super(key: key);

  @override
  State<DeviceSuspendedScreen> createState() => _DeviceSuspendedScreenState();
}

class _DeviceSuspendedScreenState extends State<DeviceSuspendedScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  final RemoteCommandService _commandService = RemoteCommandService();
  final HeartbeatService _heartbeatService = HeartbeatService();

  bool _isCheckingStatus = false;
  String _deviceId = '';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _setupCommandListener();
    _loadDeviceInfo();
    
    // Bloquear botón de retroceso
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
    _fadeController.repeat(reverse: true);
  }

  void _setupCommandListener() {
    // No configuramos callback aquí porque LauncherWrapper ya lo maneja
    // El LauncherWrapper cambiará _isDeviceSuspended a false y automáticamente
    // mostrará HomeScreen en lugar de DeviceSuspendedScreen
  }

  void _loadDeviceInfo() {
    final stats = _heartbeatService.getConnectionStats();
    setState(() {
      _deviceId = stats['deviceId'] ?? 'Desconocido';
    });
  }

  Future<void> _checkActivationStatus() async {
    if (_isCheckingStatus) return;

    setState(() {
      _isCheckingStatus = true;
    });

    try {
      // Forzar verificación de comandos
      await _commandService.forceCheckCommands();
      
      // Verificar si ya no está suspendido
      final isSuspended = await _commandService.isDeviceSuspended();
      
      if (!isSuspended && mounted) {
        Navigator.of(context).pop();
        return;
      }

      // Mostrar mensaje de verificación
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Verificando estado del dispositivo...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

    } catch (e) {
      print('❌ Error verificando estado: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingStatus = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    
    // Restaurar UI del sistema
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // Bloquear botón de retroceso
      child: Scaffold(
        backgroundColor: AppColors.error,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.error,
                AppColors.error.withOpacity(0.8),
                Colors.black87,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingXL),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icono animado
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 3,
                            ),
                          ),
                          child: const Icon(
                            Icons.block,
                            size: 60,
                            color: Colors.white,
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: AppDimensions.paddingXL),

                  // Título principal
                  AnimatedBuilder(
                    animation: _fadeAnimation,
                    builder: (context, child) {
                      return Opacity(
                        opacity: _fadeAnimation.value,
                        child: const Text(
                          'DISPOSITIVO SUSPENDIDO',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 2,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Mensaje principal
                  Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingL),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 48,
                          color: Colors.white,
                        ),
                        const SizedBox(height: AppDimensions.paddingM),
                        const Text(
                          'Este dispositivo ha sido suspendido por el administrador.',
                          style: TextStyle(
                            fontSize: 20,
                            color: Colors.white,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppDimensions.paddingM),
                        const Text(
                          'Para reactivarlo, contacte con su proveedor de servicios.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white70,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppDimensions.paddingXL),

                  // Información del dispositivo
                  Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'ID del Dispositivo:',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _deviceId,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: AppDimensions.paddingXL),

                  // Botón de verificación
                  SizedBox(
                    width: double.infinity,
                    height: 60,
                    child: ElevatedButton(
                      onPressed: _isCheckingStatus ? null : _checkActivationStatus,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppColors.error,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                        ),
                        elevation: 8,
                      ),
                      child: _isCheckingStatus
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.error),
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text(
                                  'Verificando...',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            )
                          : const Text(
                              'VERIFICAR ESTADO',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(height: AppDimensions.paddingL),

                  // Información de contacto
                  const Text(
                    'Si necesita ayuda, contacte con soporte técnico',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white60,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
