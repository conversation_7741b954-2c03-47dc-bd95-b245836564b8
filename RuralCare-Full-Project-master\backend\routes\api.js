const express = require('express');
const Database = require('../services/Database');
const router = express.Router();

// 🔍 Endpoint de prueba público
router.get('/test', async (req, res) => {
  try {
    const dbInfo = await Database.getInfo();
    
    res.json({
      success: true,
      message: 'API funcionando correctamente',
      timestamp: new Date().toISOString(),
      database: dbInfo,
      environment: process.env.NODE_ENV || 'development'
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Error conectando a la base de datos',
      details: error.message
    });
  }
});

// 📊 Estadísticas públicas básicas
router.get('/public-stats', async (req, res) => {
  try {
    const totalDevices = await Database.count('devices');
    const activeDevices = await Database.count('devices', 'status = "active"');
    
    res.json({
      success: true,
      stats: {
        total_devices: totalDevices,
        active_devices: activeDevices,
        api_version: '1.0.0'
      }
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Error obteniendo estadísticas'
    });
  }
});

module.exports = router;
