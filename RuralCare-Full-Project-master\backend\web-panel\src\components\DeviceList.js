import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh,
  Block,
  CheckCircle,
  Send,
  Message,
  Wallpaper,
  RestartAlt
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import { deviceService } from '../services/api';

const DeviceList = () => {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [commandDialog, setCommandDialog] = useState({ open: false, device: null, type: '' });
  const [commandPayload, setCommandPayload] = useState('');

  useEffect(() => {
    loadDevices();
  }, []);

  const loadDevices = async () => {
    try {
      setLoading(true);
      const response = await deviceService.getDevices();
      if (response.success) {
        setDevices(response.devices);
      } else {
        setError('Error cargando dispositivos');
      }
    } catch (error) {
      setError('Error de conexión');
      console.error('Error cargando dispositivos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSuspend = async (deviceId) => {
    try {
      await deviceService.suspendDevice(deviceId);
      loadDevices();
    } catch (error) {
      setError('Error suspendiendo dispositivo');
    }
  };

  const handleActivate = async (deviceId) => {
    try {
      await deviceService.activateDevice(deviceId);
      loadDevices();
    } catch (error) {
      setError('Error activando dispositivo');
    }
  };

  const handleSendCommand = (device, type) => {
    setCommandDialog({ open: true, device, type });
    setCommandPayload('');
  };

  const executeCommand = async () => {
    try {
      let payload = {};
      
      if (commandDialog.type === 'message') {
        payload = { message: commandPayload, flash: false };
      } else if (commandDialog.type === 'wallpaper') {
        payload = { wallpaper_url: commandPayload };
      }

      await deviceService.sendCommand(commandDialog.device.id, commandDialog.type, payload);
      setCommandDialog({ open: false, device: null, type: '' });
      setCommandPayload('');
      loadDevices();
    } catch (error) {
      setError('Error enviando comando');
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      active: { label: 'Activo', color: 'success', icon: <CheckCircle /> },
      suspended: { label: 'Suspendido', color: 'warning', icon: <Block /> },
      offline: { label: 'Offline', color: 'error', icon: <Block /> }
    };

    const config = statusConfig[status] || statusConfig.offline;
    
    return (
      <Chip
        label={config.label}
        color={config.color}
        size="small"
        icon={config.icon}
      />
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('es-ES');
  };

  const columns = [
    {
      field: 'device_name',
      headerName: 'Nombre',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" fontWeight="bold">
            {params.row.device_name || 'Sin nombre'}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            {params.row.device_id}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Estado',
      width: 120,
      renderCell: (params) => getStatusChip(params.value),
    },
    {
      field: 'model',
      headerName: 'Modelo',
      width: 150,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">
            {params.row.manufacturer} {params.row.model}
          </Typography>
          <Typography variant="caption" color="textSecondary">
            Android {params.row.android_version}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'last_seen',
      headerName: 'Última Conexión',
      width: 180,
      renderCell: (params) => (
        <Typography variant="body2">
          {formatDate(params.value)}
        </Typography>
      ),
    },
    {
      field: 'launcher_version',
      headerName: 'Versión',
      width: 100,
    },
    {
      field: 'actions',
      headerName: 'Acciones',
      width: 300,
      sortable: false,
      renderCell: (params) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          {params.row.status === 'active' ? (
            <Button
              size="small"
              variant="outlined"
              color="warning"
              onClick={() => handleSuspend(params.row.id)}
            >
              Suspender
            </Button>
          ) : (
            <Button
              size="small"
              variant="outlined"
              color="success"
              onClick={() => handleActivate(params.row.id)}
            >
              Activar
            </Button>
          )}
          
          <Tooltip title="Enviar mensaje">
            <IconButton
              size="small"
              onClick={() => handleSendCommand(params.row, 'message')}
            >
              <Message />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Cambiar fondo">
            <IconButton
              size="small"
              onClick={() => handleSendCommand(params.row, 'wallpaper')}
            >
              <Wallpaper />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Reiniciar">
            <IconButton
              size="small"
              onClick={() => handleSendCommand(params.row, 'reboot')}
            >
              <RestartAlt />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="h2">
            Dispositivos Registrados ({devices.length})
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadDevices}
          >
            Actualizar
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
            {error}
          </Alert>
        )}

        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={devices}
            columns={columns}
            pageSize={10}
            rowsPerPageOptions={[10, 25, 50]}
            disableSelectionOnClick
            sx={{
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
            }}
          />
        </Box>

        {/* Dialog para comandos */}
        <Dialog
          open={commandDialog.open}
          onClose={() => setCommandDialog({ open: false, device: null, type: '' })}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Enviar {commandDialog.type === 'message' ? 'Mensaje' : 
                   commandDialog.type === 'wallpaper' ? 'Cambiar Fondo' : 'Comando'} 
            a {commandDialog.device?.device_name}
          </DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              fullWidth
              multiline={commandDialog.type === 'message'}
              rows={commandDialog.type === 'message' ? 3 : 1}
              label={
                commandDialog.type === 'message' ? 'Mensaje' :
                commandDialog.type === 'wallpaper' ? 'URL del fondo' : 'Comando'
              }
              placeholder={
                commandDialog.type === 'message' ? 'Escribe tu mensaje aquí...' :
                commandDialog.type === 'wallpaper' ? 'https://ejemplo.com/imagen.jpg' : ''
              }
              value={commandPayload}
              onChange={(e) => setCommandPayload(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCommandDialog({ open: false, device: null, type: '' })}>
              Cancelar
            </Button>
            <Button 
              onClick={executeCommand} 
              variant="contained"
              disabled={!commandPayload.trim() && commandDialog.type !== 'reboot'}
            >
              Enviar
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default DeviceList;
