const mysql = require('mysql2/promise');

async function testActivate() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'senior_launcher'
    });
    
    console.log('🧪 Creando comando de activación manual...');
    
    // Crear comando de activación directamente
    const [result] = await connection.execute(`
      INSERT INTO remote_commands (device_id, command_type, payload, status, created_at)
      VALUES (?, ?, ?, ?, ?)
    `, [
      '65cfba18-6709-11f0-ac7a-58112232136a',
      'activate',
      JSON.stringify({
        reason: 'Prueba manual de activación',
        timestamp: new Date().toISOString()
      }),
      'pending',
      new Date()
    ]);
    
    console.log(`✅ Comando de activación creado con ID: ${result.insertId}`);
    
    // Verificar que se creó
    const [commands] = await connection.execute(`
      SELECT * FROM remote_commands WHERE id = ?
    `, [result.insertId]);
    
    console.log('📋 Comando creado:', commands[0]);
    
    await connection.end();
    
    console.log('\n🎯 Ahora el launcher debería recibir el comando en máximo 30 segundos');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testActivate();
