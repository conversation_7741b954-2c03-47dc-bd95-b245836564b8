import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_dimensions.dart';
import '../constants/app_categories.dart';
import '../services/app_management_service.dart';
import '../models/app_category.dart';

/// Categorías de navegación para la barra lateral
class NavigationCategory {
  final String id;
  final String label;
  final IconData icon;
  final Color color;
  final int appCount;

  const NavigationCategory({
    required this.id,
    required this.label,
    required this.icon,
    required this.color,
    this.appCount = 0,
  });

  // Categorías especiales
  static const favorites = NavigationCategory(
    id: 'favorites',
    label: 'Favoritas',
    icon: Icons.star,
    color: Color(0xFFFFD700),
  );

  static const allApps = NavigationCategory(
    id: 'all',
    label: 'Todas',
    icon: Icons.apps,
    color: Color(0xFF795548),
  );
}

/// Barra lateral de navegación con categorías de aplicaciones
class SidebarNavigation extends StatefulWidget {
  final NavigationCategory selectedCategory;
  final Function(NavigationCategory) onCategorySelected;

  const SidebarNavigation({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  State<SidebarNavigation> createState() => _SidebarNavigationState();
}

class _SidebarNavigationState extends State<SidebarNavigation> {
  List<NavigationCategory> _categories = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  void _loadCategories() {
    final stats = AppManagementService.instance.getAppStatistics();

    _categories = [
      // Favoritos
      NavigationCategory(
        id: 'favorites',
        label: 'Favoritos',
        icon: Icons.star,
        color: const Color(0xFFFFD700),
        appCount: stats['Favoritos'] ?? 0,
      ),

      // Categorías dinámicas desde AppCategories (excluyendo favoritos para evitar duplicación)
      ...AppCategories.allCategories
          .where((category) => category.id != 'favorites') // Excluir favoritos
          .map((category) => NavigationCategory(
            id: category.id,
            label: category.name,
            icon: category.icon,
            color: category.color,
            appCount: stats[category.name] ?? 0,
          ))
          .where((cat) => cat.appCount > 0), // Solo mostrar categorías con apps

      // Todas las apps
      NavigationCategory(
        id: 'all',
        label: 'Todas',
        icon: Icons.apps,
        color: const Color(0xFF795548),
        appCount: stats['Total'] ?? 0,
      ),
    ];

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      decoration: BoxDecoration(
        color: AppColors.surface.withOpacity(0.95),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(height: AppDimensions.paddingS),
          
          // Categorías de navegación
          Expanded(
            child: ListView.builder(
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = category.id == widget.selectedCategory.id;

                return _buildCategoryItem(category, isSelected);
              },
            ),
          ),
          
          const SizedBox(height: AppDimensions.paddingS),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(NavigationCategory category, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 4,
      ),
      child: Material(
        color: isSelected ? category.color.withOpacity(0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: InkWell(
          onTap: () => widget.onCategorySelected(category),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icono de la categoría
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected ? category.color : category.color.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: category.color.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: Icon(
                    category.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // Etiqueta de la categoría
                Text(
                  category.label,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                    color: isSelected ? category.color : AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                // Contador de apps
                if (category.appCount > 0)
                  Container(
                    margin: const EdgeInsets.only(top: 2),
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: category.color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${category.appCount}',
                      style: TextStyle(
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                        color: category.color,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
