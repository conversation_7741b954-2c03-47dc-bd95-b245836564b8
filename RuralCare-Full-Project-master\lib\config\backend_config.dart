import 'environment_config.dart';

/// Configuración del backend
class BackendConfig {
  // Usar configuración de entorno
  static String get baseUrl => EnvironmentConfig.backendUrl;
  static Duration get connectionTimeout => EnvironmentConfig.connectionTimeout;
  static Duration get heartbeatInterval => EnvironmentConfig.heartbeatInterval;
  static Duration get commandCheckInterval => EnvironmentConfig.commandCheckInterval;
  static int get maxRetries => EnvironmentConfig.maxRetries;
  static Duration get retryDelay => EnvironmentConfig.retryDelay;
  static bool get enableDebugLogs => EnvironmentConfig.enableDebugLogs;

  /// Obtener la URL base según el entorno
  static String getBaseUrl() => baseUrl;

  /// Verificar si estamos en modo desarrollo
  static bool get isDebugMode => EnvironmentConfig.enableDebugLogs;

  /// Verificar si requiere HTTPS
  static bool get requireHttps => EnvironmentConfig.requireHttps;

  /// Verificar si SSL pinning está habilitado
  static bool get enableSSLPinning => EnvironmentConfig.enableSSLPinning;
}
