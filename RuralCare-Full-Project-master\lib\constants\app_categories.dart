import 'package:flutter/material.dart';
import '../models/app_category.dart';
import 'app_colors.dart';

/// Constantes de categorías de aplicaciones para el Senior Launcher
class AppCategories {
  // Categorías principales
  static const AppCategory favorites = AppCategory(
    id: 'favorites',
    name: 'Favoritos',
    displayName: 'Mis Favoritos',
    icon: Icons.star,
    color: AppColors.warning,
    packageNames: [],
    description: 'Aplicaciones favoritas del usuario',
  );

  static const AppCategory communication = AppCategory(
    id: 'communication',
    name: 'Comunicación',
    displayName: 'Comunicación',
    icon: Icons.chat,
    color: AppColors.success,
    packageNames: [
      'com.android.dialer',
      'com.android.mms',
      'com.whatsapp',
      'com.facebook.orca',
      'com.skype.raider',
      'com.viber.voip',
      'org.telegram.messenger',
      'com.google.android.gm',
      'com.microsoft.office.outlook',
      'com.android.contacts',
    ],
    description: 'Llamadas, mensajes y email',
  );

  static const AppCategory entertainment = AppCategory(
    id: 'entertainment',
    name: 'Entretenimiento',
    displayName: 'Entretenimiento',
    icon: Icons.play_circle_fill,
    color: AppColors.error,
    packageNames: [
      'com.google.android.youtube',
      'com.netflix.mediaclient',
      'com.spotify.music',
      'com.amazon.avod.thirdpartyclient',
      'com.disney.disneyplus',
      'com.google.android.apps.youtube.music',
      'com.android.music',
      'com.google.android.apps.photos',
      'com.android.gallery3d',
    ],
    description: 'Videos, música y fotos',
  );

  static const AppCategory productivity = AppCategory(
    id: 'productivity',
    name: 'Productividad',
    displayName: 'Productividad',
    icon: Icons.work,
    color: AppColors.primary,
    packageNames: [
      'com.microsoft.office.word',
      'com.microsoft.office.excel',
      'com.microsoft.office.powerpoint',
      'com.google.android.apps.docs.editors.docs',
      'com.google.android.apps.docs.editors.sheets',
      'com.adobe.reader',
      'com.dropbox.android',
      'com.google.android.apps.drive',
    ],
    description: 'Documentos, notas y trabajo',
  );

  static const AppCategory health = AppCategory(
    id: 'health',
    name: 'Salud',
    displayName: 'Salud y Bienestar',
    icon: Icons.favorite,
    color: AppColors.accent,
    packageNames: [
      'com.fitbit.FitbitMobile',
      'com.samsung.android.app.shealth',
      'com.google.android.apps.fitness',
      'com.android.emergency',
      'com.sec.android.app.safetyassurance',
    ],
    description: 'Salud, ejercicio y emergencias',
  );

  static const AppCategory utilities = AppCategory(
    id: 'utilities',
    name: 'Utilidades',
    displayName: 'Herramientas',
    icon: Icons.build,
    color: AppColors.info,
    packageNames: [
      'com.android.camera2',
      'com.google.android.calculator',
      'com.android.calendar',
      'com.google.android.apps.maps',
      'com.waze',
      'com.android.chrome',
      'com.google.android.flashlight',
      'com.android.settings',
    ],
    description: 'Cámara, calculadora y utilidades',
  );

  static const AppCategory social = AppCategory(
    id: 'social',
    name: 'Redes Sociales',
    displayName: 'Redes Sociales',
    icon: Icons.people,
    color: AppColors.primaryLight,
    packageNames: [
      'com.facebook.katana',
      'com.instagram.android',
      'com.twitter.android',
      'com.linkedin.android',
      'com.pinterest',
      'com.snapchat.android',
    ],
    description: 'Facebook, Instagram y más',
  );

  static const AppCategory shopping = AppCategory(
    id: 'shopping',
    name: 'Compras',
    displayName: 'Compras',
    icon: Icons.shopping_cart,
    color: AppColors.warning,
    packageNames: [
      'com.amazon.mShop.android.shopping',
      'com.paypal.android.p2pmobile',
      'com.mercadolibre',
      'com.ebay.mobile',
    ],
    description: 'Tiendas online y pagos',
  );

  static const AppCategory others = AppCategory(
    id: 'others',
    name: 'Otras',
    displayName: 'Otras Aplicaciones',
    icon: Icons.apps,
    color: AppColors.textSecondary,
    packageNames: [],
    description: 'Aplicaciones sin categoría específica',
  );

  /// Lista de todas las categorías disponibles
  static const List<AppCategory> allCategories = [
    favorites,
    communication,
    entertainment,
    productivity,
    health,
    utilities,
    social,
    shopping,
    others,
  ];

  /// Obtener categoría por ID
  static AppCategory getCategoryById(String id) {
    return allCategories.firstWhere(
      (category) => category.id == id,
      orElse: () => others,
    );
  }

  /// Obtener categoría por package name
  static AppCategory getCategoryForPackage(String packageName) {
    for (final category in allCategories) {
      if (category.packageNames.contains(packageName)) {
        return category;
      }
    }
    return others;
  }

  /// Categorías principales para la navegación
  static const List<AppCategory> mainCategories = [
    favorites,
    communication,
    entertainment,
    utilities,
    health,
  ];

  /// Categorías secundarias
  static const List<AppCategory> secondaryCategories = [
    productivity,
    social,
    shopping,
    others,
  ];
}
