const express = require('express');
const Database = require('../services/Database');
const AuthService = require('../services/AuthService');
const router = express.Router();

// Middleware de autenticación para rutas admin
const requireAuth = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Token requerido' });
    }
    
    const user = await AuthService.validateSession(token);
    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ error: error.message });
  }
};

// Aplicar middleware a todas las rutas
router.use(requireAuth);

// 📱 Listar dispositivos
router.get('/devices', async (req, res) => {
  try {
    const devices = await Database.findMany(
      'devices',
      '1=1',
      [],
      'last_seen DESC'
    );
    
    res.json({
      success: true,
      devices: devices
    });
    
  } catch (error) {
    console.error('Error obteniendo dispositivos:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// 🚫 Suspender dispositivo
router.post('/devices/:id/suspend', async (req, res) => {
  try {
    const { id } = req.params;

    // Obtener información del dispositivo
    const device = await Database.findOne('devices', 'id = ?', [id]);
    if (!device) {
      return res.status(404).json({
        error: 'Dispositivo no encontrado'
      });
    }

    // Actualizar estado del dispositivo
    await Database.update('devices', {
      status: 'suspended'
    }, 'id = ?', [id]);

    // Crear comando remoto de suspensión
    console.log(`🔍 Debug: Creando comando para device UUID: ${device.id} (${device.device_id})`);

    const commandData = {
      device_id: device.id, // Usar UUID, no device_id legible
      command_type: 'suspend',
      payload: JSON.stringify({
        reason: 'Suspendido por administrador',
        timestamp: new Date().toISOString()
      }),
      status: 'pending',
      created_at: new Date()
    };

    console.log(`🔍 Debug: Datos del comando:`, commandData);

    const commandResult = await Database.insert('remote_commands', commandData);

    console.log(`📱 Dispositivo ${device.device_id} suspendido y comando creado con ID: ${commandResult}`);

    res.json({
      success: true,
      message: 'Dispositivo suspendido'
    });

  } catch (error) {
    console.error('Error suspendiendo dispositivo:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// ✅ Activar dispositivo
router.post('/devices/:id/activate', async (req, res) => {
  try {
    const { id } = req.params;

    // Obtener información del dispositivo
    const device = await Database.findOne('devices', 'id = ?', [id]);
    if (!device) {
      return res.status(404).json({
        error: 'Dispositivo no encontrado'
      });
    }

    // Actualizar estado del dispositivo
    await Database.update('devices', {
      status: 'active'
    }, 'id = ?', [id]);

    // Crear comando remoto de activación
    console.log(`🔍 Debug: Creando comando de activación para device UUID: ${device.id} (${device.device_id})`);

    const commandData = {
      device_id: device.id, // Usar UUID, no device_id legible
      command_type: 'activate',
      payload: JSON.stringify({
        reason: 'Activado por administrador',
        timestamp: new Date().toISOString()
      }),
      status: 'pending',
      created_at: new Date()
    };

    console.log(`🔍 Debug: Datos del comando:`, commandData);

    const commandResult = await Database.insert('remote_commands', commandData);

    console.log(`📱 Dispositivo ${device.device_id} activado y comando creado con ID: ${commandResult}`);

    res.json({
      success: true,
      message: 'Dispositivo activado'
    });

  } catch (error) {
    console.error('Error activando dispositivo:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// 📡 Enviar comando a dispositivo
router.post('/devices/:id/command', async (req, res) => {
  try {
    const { id } = req.params;
    const { command_type, payload } = req.body;
    
    if (!command_type) {
      return res.status(400).json({
        error: 'command_type es requerido'
      });
    }
    
    // Verificar que el dispositivo existe
    const device = await Database.findOne('devices', 'id = ?', [id]);
    if (!device) {
      return res.status(404).json({
        error: 'Dispositivo no encontrado'
      });
    }
    
    // Crear comando
    const commandId = await Database.insert('remote_commands', {
      device_id: id,
      command_type,
      payload: JSON.stringify(payload || {}),
      status: 'pending'
    });
    
    res.json({
      success: true,
      message: 'Comando enviado',
      command_id: commandId
    });
    
  } catch (error) {
    console.error('Error enviando comando:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

// 📊 Obtener estadísticas
router.get('/stats', async (req, res) => {
  try {
    const totalDevices = await Database.count('devices');
    const activeDevices = await Database.count('devices', 'status = "active"');
    const suspendedDevices = await Database.count('devices', 'status = "suspended"');
    const offlineDevices = await Database.count('devices', 'status = "offline"');
    
    const pendingCommands = await Database.count('remote_commands', 'status = "pending"');
    const executedCommands = await Database.count('remote_commands', 'status = "delivered"');
    
    res.json({
      success: true,
      stats: {
        devices: {
          total: totalDevices,
          active: activeDevices,
          suspended: suspendedDevices,
          offline: offlineDevices
        },
        commands: {
          pending: pendingCommands,
          executed: executedCommands
        }
      }
    });
    
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({
      error: 'Error interno del servidor'
    });
  }
});

module.exports = router;
