import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:typed_data';
import '../models/app_item.dart';
import '../models/app_category.dart';
import '../constants/app_categories.dart';

/// Servicio completo para gestión de aplicaciones
/// Incluye agregar/quitar, categorización automática, favoritos y búsqueda
class AppManagementService {
  static const MethodChannel _channel = MethodChannel('ruralcare/app_management');
  
  static AppManagementService? _instance;
  static AppManagementService get instance => _instance ??= AppManagementService._();
  
  AppManagementService._();

  SharedPreferences? _prefs;
  List<AppItem> _allInstalledApps = [];
  List<AppItem> _visibleApps = [];
  List<String> _favoriteAppIds = [];
  List<String> _hiddenAppIds = [];

  // Claves para SharedPreferences
  static const String _favoriteAppsKey = 'favorite_apps';
  static const String _hiddenAppsKey = 'hidden_apps';
  static const String _customCategoriesKey = 'custom_categories';

  /// Inicializar el servicio
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadPreferences();
      await refreshInstalledApps();
      await _loadCustomCategories();
      print('✅ AppManagementService inicializado correctamente');
    } catch (e) {
      print('❌ Error inicializando AppManagementService: $e');
    }
  }

  /// Cargar preferencias guardadas
  Future<void> _loadPreferences() async {
    _favoriteAppIds = _prefs?.getStringList(_favoriteAppsKey) ?? [];
    _hiddenAppIds = _prefs?.getStringList(_hiddenAppsKey) ?? [];
  }

  /// Obtener todas las aplicaciones instaladas del sistema
  Future<List<AppItem>> getAllInstalledApps() async {
    try {
      final result = await _channel.invokeMethod('getAllInstalledApps');
      final List<dynamic> appsData = result ?? [];
      
      _allInstalledApps = appsData.map((appData) {
        return AppItem(
          id: appData['packageName'] ?? '',
          name: appData['appName'] ?? 'App Desconocida',
          packageName: appData['packageName'] ?? '',
          category: _categorizeApp(appData['packageName'], appData['appName']),
          icon: Icons.android, // Icono por defecto
          color: Colors.blue, // Color por defecto
          appIcon: appData['icon'] != null ? Uint8List.fromList(List<int>.from(appData['icon'])) : null,
        );
      }).toList();

      // Aplicar filtros de apps ocultas
      _visibleApps = _allInstalledApps.where((app) => !_hiddenAppIds.contains(app.id)).toList();
      
      return _allInstalledApps;
    } catch (e) {
      print('Error obteniendo apps instaladas: $e');
      return [];
    }
  }

  /// Refrescar lista de aplicaciones instaladas
  Future<void> refreshInstalledApps() async {
    await getAllInstalledApps();
  }

  /// Categorizar automáticamente una aplicación
  AppCategory _categorizeApp(String packageName, String appName) {
    final packageLower = packageName.toLowerCase();
    final nameLower = appName.toLowerCase();

    // Comunicación
    if (_containsAny(packageLower, ['whatsapp', 'telegram', 'messenger', 'sms', 'phone', 'contacts', 'call']) ||
        _containsAny(nameLower, ['whatsapp', 'telegram', 'messenger', 'teléfono', 'contactos', 'llamada'])) {
      return AppCategories.communication;
    }

    // Entretenimiento
    if (_containsAny(packageLower, ['youtube', 'netflix', 'spotify', 'music', 'video', 'game', 'play']) ||
        _containsAny(nameLower, ['youtube', 'netflix', 'spotify', 'música', 'video', 'juego'])) {
      return AppCategories.entertainment;
    }

    // Productividad
    if (_containsAny(packageLower, ['office', 'word', 'excel', 'pdf', 'calendar', 'note', 'drive']) ||
        _containsAny(nameLower, ['office', 'word', 'excel', 'pdf', 'calendario', 'nota'])) {
      return AppCategories.productivity;
    }

    // Salud
    if (_containsAny(packageLower, ['health', 'medical', 'fitness', 'doctor', 'medicine']) ||
        _containsAny(nameLower, ['salud', 'médico', 'medicina', 'doctor', 'fitness'])) {
      return AppCategories.health;
    }

    // Utilidades
    if (_containsAny(packageLower, ['settings', 'calculator', 'clock', 'weather', 'flashlight', 'camera']) ||
        _containsAny(nameLower, ['configuración', 'calculadora', 'reloj', 'clima', 'linterna', 'cámara'])) {
      return AppCategories.utilities;
    }

    // Por defecto: Otras
    return AppCategories.others;
  }

  /// Verificar si una cadena contiene alguna de las palabras clave
  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  /// Obtener aplicaciones visibles (no ocultas)
  List<AppItem> getVisibleApps() {
    return _visibleApps;
  }

  /// Obtener aplicaciones por categoría
  List<AppItem> getAppsByCategory(AppCategory category) {
    return _visibleApps.where((app) => app.category.id == category.id).toList();
  }

  /// Obtener aplicaciones favoritas
  List<AppItem> getFavoriteApps() {
    return _visibleApps.where((app) => _favoriteAppIds.contains(app.id)).toList();
  }

  /// Agregar aplicación a favoritos
  Future<void> addToFavorites(String appId) async {
    if (!_favoriteAppIds.contains(appId)) {
      _favoriteAppIds.add(appId);
      await _prefs?.setStringList(_favoriteAppsKey, _favoriteAppIds);
    }
  }

  /// Quitar aplicación de favoritos
  Future<void> removeFromFavorites(String appId) async {
    _favoriteAppIds.remove(appId);
    await _prefs?.setStringList(_favoriteAppsKey, _favoriteAppIds);
  }

  /// Verificar si una app es favorita
  bool isFavorite(String appId) {
    return _favoriteAppIds.contains(appId);
  }

  /// Ocultar aplicación
  Future<void> hideApp(String appId) async {
    if (!_hiddenAppIds.contains(appId)) {
      _hiddenAppIds.add(appId);
      await _prefs?.setStringList(_hiddenAppsKey, _hiddenAppIds);
      
      // Actualizar lista visible
      _visibleApps = _allInstalledApps.where((app) => !_hiddenAppIds.contains(app.id)).toList();
    }
  }

  /// Mostrar aplicación oculta
  Future<void> showApp(String appId) async {
    _hiddenAppIds.remove(appId);
    await _prefs?.setStringList(_hiddenAppsKey, _hiddenAppIds);
    
    // Actualizar lista visible
    _visibleApps = _allInstalledApps.where((app) => !_hiddenAppIds.contains(app.id)).toList();
  }

  /// Verificar si una app está oculta
  bool isHidden(String appId) {
    return _hiddenAppIds.contains(appId);
  }

  /// Buscar aplicaciones por nombre
  List<AppItem> searchApps(String query) {
    if (query.isEmpty) return _visibleApps;
    
    final queryLower = query.toLowerCase();
    return _visibleApps.where((app) {
      return app.name.toLowerCase().contains(queryLower) ||
             app.packageName.toLowerCase().contains(queryLower);
    }).toList();
  }

  /// Obtener estadísticas de aplicaciones
  Map<String, int> getAppStatistics() {
    final stats = <String, int>{};
    
    for (final category in AppCategories.allCategories) {
      stats[category.name] = getAppsByCategory(category).length;
    }
    
    stats['Total'] = _visibleApps.length;
    stats['Favoritos'] = _favoriteAppIds.length;
    stats['Ocultas'] = _hiddenAppIds.length;
    stats['Instaladas'] = _allInstalledApps.length;
    
    return stats;
  }

  /// Recategorizar aplicación manualmente
  Future<void> recategorizeApp(String appId, AppCategory newCategory) async {
    // Encontrar la app y actualizar su categoría
    final appIndex = _allInstalledApps.indexWhere((app) => app.id == appId);
    if (appIndex != -1) {
      _allInstalledApps[appIndex] = _allInstalledApps[appIndex].copyWith(category: newCategory);
      
      // Actualizar también en la lista visible
      final visibleIndex = _visibleApps.indexWhere((app) => app.id == appId);
      if (visibleIndex != -1) {
        _visibleApps[visibleIndex] = _visibleApps[visibleIndex].copyWith(category: newCategory);
      }
      
      // Guardar categorías personalizadas
      await _saveCustomCategories();
    }
  }

  /// Guardar categorías personalizadas
  Future<void> _saveCustomCategories() async {
    final customCategories = <String, String>{};
    
    for (final app in _allInstalledApps) {
      // Solo guardar si la categoría fue cambiada manualmente
      final autoCategory = _categorizeApp(app.packageName, app.name);
      if (app.category.id != autoCategory.id) {
        customCategories[app.id] = app.category.id;
      }
    }
    
    final categoriesJson = customCategories.entries
        .map((e) => '${e.key}:${e.value}')
        .toList();
    
    await _prefs?.setStringList(_customCategoriesKey, categoriesJson);
  }

  /// Cargar categorías personalizadas
  Future<void> _loadCustomCategories() async {
    final categoriesJson = _prefs?.getStringList(_customCategoriesKey) ?? [];
    final customCategories = <String, String>{};
    
    for (final entry in categoriesJson) {
      final parts = entry.split(':');
      if (parts.length == 2) {
        customCategories[parts[0]] = parts[1];
      }
    }
    
    // Aplicar categorías personalizadas
    for (final app in _allInstalledApps) {
      if (customCategories.containsKey(app.id)) {
        final categoryId = customCategories[app.id]!;
        final category = AppCategories.allCategories.firstWhere(
          (cat) => cat.id == categoryId,
          orElse: () => AppCategories.others,
        );
        
        // Actualizar categoría
        final appIndex = _allInstalledApps.indexWhere((a) => a.id == app.id);
        if (appIndex != -1) {
          _allInstalledApps[appIndex] = _allInstalledApps[appIndex].copyWith(category: category);
        }
      }
    }
  }
}
