# 🌐 Configuración del Servidor
NODE_ENV=development
PORT=3000

# 🔐 Seguridad
JWT_SECRET=3ae3f5a9f885c670783723b42c06f5d7c4a39871773e69a4ad372f386e39d7ee
JWT_EXPIRES_IN=24h

# 🗄️ Configuración MySQL (BanaHosting)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=senior_launcher
DB_USER=root
DB_PASSWORD=""

# 🌐 CORS (dominios permitidos)
CORS_ORIGINS=http://localhost:3000,https://tu-dominio.com

# 📊 Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 📁 Subida de archivos
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# 🕐 Configuración de timeouts
HEARTBEAT_TIMEOUT_MINUTES=10
COMMAND_TIMEOUT_MINUTES=30

# 🔧 Configuración para BanaHosting
# Descomenta y configura para producción:
# NODE_ENV=production
# DB_HOST=tu-servidor-mysql.banahosting.com
# DB_NAME=tu_base_datos
# DB_USER=tu_usuario
# DB_PASSWORD=tu_password_seguro
# JWT_SECRET=clave-jwt-super-segura-para-produccion
# CORS_ORIGINS=https://tu-dominio.com
