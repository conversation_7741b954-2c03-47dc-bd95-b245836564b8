const crypto = require('crypto');
const jwt = require('jsonwebtoken'); // <-- You need this library!

// Generate a random secret (you'd typically keep this constant or load from env)
const jwtSecret = crypto.randomBytes(32).toString('hex');
console.log('JWT Secret:', jwtSecret); // Log the secret so you can use it in jwt.io

// Define your payload (the data you want to store in the token)
const payload = {
    userId: '123',
    username: 'testuser',
    role: 'admin'
};

// Define options for signing (e.g., expiration)
const options = {
    expiresIn: '1h' // Token expires in 1 hour
};

// Generate the JWT
const token = jwt.sign(payload, jwtSecret, options);

console.log('Generated JWT:', token); // <-- THIS IS THE STRING YOU PASTE INTO JWT.IO